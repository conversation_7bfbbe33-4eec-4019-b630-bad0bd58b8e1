import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';

/// Filter bottom sheet for customer list
class CustomerFilterSheet extends StatefulWidget {
  const CustomerFilterSheet({super.key});

  @override
  State<CustomerFilterSheet> createState() => _CustomerFilterSheetState();
}

class _CustomerFilterSheetState extends State<CustomerFilterSheet> {
  String? selectedCustomerType;
  String? selectedRiskLevel;
  String? selectedCategory;
  String? selectedSortBy;
  bool ascending = true;

  final List<String> customerTypes = ['Individual', 'Business'];
  final List<String> riskLevels = ['Low', 'Medium', 'High', 'Critical'];
  final List<String> categories = ['Regular', 'VIP', 'New'];
  final List<String> sortOptions = ['Name', 'Created Date', 'Total Debt'];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Text(
                  'Filter Customers',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: AppThemes.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          Divider(color: Theme.of(context).colorScheme.outline),
          
          // Filter options
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Customer Type Filter
                  _buildFilterSection(
                    context: context,
                    title: 'Customer Type',
                    options: customerTypes,
                    selectedValue: selectedCustomerType,
                    onChanged: (value) {
                      setState(() {
                        selectedCustomerType = value;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Risk Level Filter
                  _buildFilterSection(
                    context: context,
                    title: 'Risk Level',
                    options: riskLevels,
                    selectedValue: selectedRiskLevel,
                    onChanged: (value) {
                      setState(() {
                        selectedRiskLevel = value;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Category Filter
                  _buildFilterSection(
                    context: context,
                    title: 'Category',
                    options: categories,
                    selectedValue: selectedCategory,
                    onChanged: (value) {
                      setState(() {
                        selectedCategory = value;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Sort Options
                  _buildSortSection(context),
                ],
              ),
            ),
          ),
          
          // Action buttons
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: CommonButton(
                    label: 'Reset',
                    type: ButtonType.outlined,
                    onPressed: _resetFilters,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CommonButton(
                    label: 'Apply Filters',
                    onPressed: _applyFilters,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection({
    required BuildContext context,
    required String title,
    required List<String> options,
    required String? selectedValue,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = selectedValue == option;
            return GestureDetector(
              onTap: () {
                onChanged(isSelected ? null : option);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppThemes.primaryColor.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surface,
                  border: Border.all(
                    color: isSelected 
                        ? AppThemes.primaryColor 
                        : Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected 
                        ? AppThemes.primaryColor 
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSortSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort By',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        
        // Sort options
        ...sortOptions.map((option) {
          final isSelected = selectedSortBy == option;
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: () {
                setState(() {
                  selectedSortBy = isSelected ? null : option;
                });
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppThemes.primaryColor.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surface,
                  border: Border.all(
                    color: isSelected 
                        ? AppThemes.primaryColor 
                        : Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      color: isSelected 
                          ? AppThemes.primaryColor 
                          : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          color: isSelected 
                              ? AppThemes.primaryColor 
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
        
        // Sort direction
        if (selectedSortBy != null) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                'Sort Direction:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            ascending = true;
                          });
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: ascending 
                                ? AppThemes.primaryColor.withOpacity(0.1)
                                : Theme.of(context).colorScheme.surface,
                            border: Border.all(
                              color: ascending 
                                  ? AppThemes.primaryColor 
                                  : Theme.of(context).colorScheme.outline,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.arrow_upward,
                                size: 16,
                                color: ascending 
                                    ? AppThemes.primaryColor 
                                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Ascending',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: ascending 
                                      ? AppThemes.primaryColor 
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            ascending = false;
                          });
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: !ascending 
                                ? AppThemes.primaryColor.withOpacity(0.1)
                                : Theme.of(context).colorScheme.surface,
                            border: Border.all(
                              color: !ascending 
                                  ? AppThemes.primaryColor 
                                  : Theme.of(context).colorScheme.outline,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.arrow_downward,
                                size: 16,
                                color: !ascending 
                                    ? AppThemes.primaryColor 
                                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Descending',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: !ascending 
                                      ? AppThemes.primaryColor 
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  void _clearFilters() {
    setState(() {
      selectedCustomerType = null;
      selectedRiskLevel = null;
      selectedCategory = null;
      selectedSortBy = null;
      ascending = true;
    });
  }

  void _resetFilters() {
    _clearFilters();
    context.read<CustomerBloc>().add(const LoadCustomers());
    Navigator.pop(context);
  }

  void _applyFilters() {
    // Apply filters to customer list
    context.read<CustomerBloc>().add(
          LoadCustomers(
            // Add filter parameters when backend supports them
            sortBy: selectedSortBy?.toLowerCase(),
            ascending: ascending,
          ),
        );
    Navigator.pop(context);
  }
} 