import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart' hide CommonFAB;
import 'package:deyncare_app/presentation/widgets/common_list_item.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';
import 'package:deyncare_app/presentation/widgets/skeleton_loader.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_bloc.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_state.dart';
import 'package:deyncare_app/presentation/blocs/customer/customer_event.dart';
import 'package:deyncare_app/presentation/screens/customer/widgets/customer_search_bar.dart';
import 'package:deyncare_app/presentation/screens/customer/widgets/customer_filter_sheet.dart';
import 'package:deyncare_app/presentation/widgets/modals/customer/customer_modal_handlers.dart';
import 'package:deyncare_app/domain/models/customer_models.dart';
import 'package:deyncare_app/injection_container.dart' as di;

/// Main customer list screen displaying all customers with search and filter
class CustomerListScreen extends StatelessWidget {
  const CustomerListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<CustomerBloc>()..add(const LoadCustomers()),
      child: const CustomerListView(),
    );
  }
}

class CustomerListView extends StatefulWidget {
  const CustomerListView({super.key});

  @override
  State<CustomerListView> createState() => _CustomerListViewState();
}

class _CustomerListViewState extends State<CustomerListView> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    // Remove infinite scroll listener
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh data when screen becomes visible again
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final state = context.read<CustomerBloc>().state;
      if (state is! CustomerListLoaded) {
        context.read<CustomerBloc>().add(const LoadCustomers());
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // Remove the infinite scroll method
  // void _onScroll() { ... }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CommonAppBar(
        title: 'Customers',
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh_rounded,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () => _refreshCustomers(context),
            tooltip: 'Refresh',
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.filter_list_rounded,
              color: Theme.of(context).appBarTheme.iconTheme?.color,
            ),
            onPressed: () => _showFilterSheet(context),
            tooltip: 'Filter',
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.person_add_rounded,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: () => _navigateToCreateCustomer(context),
            tooltip: 'Add Customer',
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          CustomerSearchBar(
            controller: _searchController,
            onChanged: _onSearchChanged,
          ),
          
          // Customer list with pagination
          Expanded(
            child: BlocConsumer<CustomerBloc, CustomerState>(
              buildWhen: (previous, current) =>
                  current is CustomerListLoaded ||
                  current is CustomerListLoading ||
                  current is CustomerListError,
              listenWhen: (previous, current) =>
                  current is CustomerError ||
                  current is CustomerCreated ||
                  current is CustomerUpdated ||
                  current is CustomerDeleted,
              listener: _handleStateChanges,
              builder: (context, state) => _buildBody(context, state),
            ),
          ),
          
          // Pagination controls (will be shown only when list is loaded)
          BlocBuilder<CustomerBloc, CustomerState>(
            buildWhen: (previous, current) => current is CustomerListLoaded,
            builder: (context, state) {
              if (state is CustomerListLoaded) {
                return _buildPaginationControls(context, state);
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  void _handleStateChanges(BuildContext context, CustomerState state) {
    if (state is CustomerError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (state is CustomerCreated) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Customer created successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      // Refresh the list
      _refreshCustomers(context);
    } else if (state is CustomerUpdated) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Customer updated successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      // Refresh the list to show updated data
      _refreshCustomers(context);
    } else if (state is CustomerDeleted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Customer deleted successfully!'),
          backgroundColor: AppThemes.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      // Refresh the list to remove deleted customer
      _refreshCustomers(context);
    }
  }

  void _refreshCustomers(BuildContext context) {
    // Clear search if active
    if (_searchController.text.isNotEmpty) {
      _searchController.clear();
    }
    
    // Trigger refresh
    context.read<CustomerBloc>().add(const RefreshCustomers());
    
    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Refreshing customer list...'),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Refresh method for RefreshIndicator (no parameters, returns Future)
  Future<void> _handleRefresh() async {
    // Clear search if active
    if (_searchController.text.isNotEmpty) {
      _searchController.clear();
    }
    
    // Trigger refresh
    context.read<CustomerBloc>().add(const RefreshCustomers());
    
    // Wait a bit for the refresh to complete
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Handle refresh button press
  void _handleRefreshPress() {
    _refreshCustomers(context);
  }

  Widget _buildBody(BuildContext context, CustomerState state) {
    if (state is CustomerListLoading) {
      return _buildLoadingState();
    }
    
    if (state is CustomerListError) {
      return _buildErrorState(state.message);
    }
    
    if (state is CustomerListLoaded) {
      return _buildLoadedState(context, state);
    }
    
    // If it's initial state or other state, show loading
    return _buildLoadingState();
  }

  /// Enhanced loading state with skeleton loaders
  Widget _buildLoadingState() {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: SkeletonListView(
        itemCount: 8,
        padding: const EdgeInsets.all(16),
        itemBuilder: (index) => _buildCustomerItemSkeleton(),
      ),
    );
  }

  /// Customer item skeleton
  Widget _buildCustomerItemSkeleton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Avatar skeleton
            SkeletonLoader.circular(size: 48),
            const SizedBox(width: 16),
            
            // Content skeleton
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SkeletonLoader.text(width: double.infinity, height: 18),
                  const SizedBox(height: 4),
                  SkeletonLoader.text(width: 150, height: 14),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      SkeletonLoader.text(width: 60, height: 20),
                      const SizedBox(width: 8),
                      SkeletonLoader.text(width: 80, height: 16),
                    ],
                  ),
                ],
              ),
            ),
            
            // Actions skeleton
            Column(
              children: [
                SkeletonLoader.circular(size: 32),
                const SizedBox(height: 8),
                SkeletonLoader.circular(size: 32),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Enhanced empty state
  Widget _buildEmptyState() {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  Icons.people_outline_rounded,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'No Customers Yet',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Start by adding your first customer to begin managing debts and payments.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => CustomerModalHandlers.showAddCustomerModal(context),
                icon: const Icon(Icons.person_add_rounded),
                label: const Text('Add Your First Customer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Enhanced error state
  Widget _buildErrorState(String error) {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  Icons.error_outline_rounded,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Unable to Load Customers',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                error,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: _handleRefreshPress,
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, CustomerListLoaded state) {
    final customers = state.response.data.customers ?? [];
    
    if (customers.isEmpty) {
      return _buildEmptyState();
    }
    
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      color: Theme.of(context).colorScheme.primary,
      backgroundColor: Theme.of(context).colorScheme.surface,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: customers.length, // Remove infinite scroll item count
        itemBuilder: (context, index) {
          final customer = customers[index];
          return CustomerListItem(
            customerId: customer.customerId,
            customerName: customer.customerName,
            customerType: customer.customerType,
            phone: customer.phone,
            totalDebt: customer.totalDebt,
            onTap: () => _navigateToCustomerDetails(context, customer.customerId),
            actions: [
              // View Action with Label
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.visibility_rounded, size: 18, color: Theme.of(context).colorScheme.primary),
                    ),
                    onPressed: () => _navigateToCustomerDetails(context, customer.customerId),
                    tooltip: 'View Details',
                  ),
                  Text(
                    'View',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              // Edit Action with Label
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppThemes.warningColor.withValues(alpha: 0.1),
                            AppThemes.warningColor.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: AppThemes.warningColor.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.edit_rounded, size: 18, color: AppThemes.warningColor),
                    ),
                    onPressed: () => _navigateToEditCustomer(context, customer.customerId),
                    tooltip: 'Edit Customer',
                  ),
                  Text(
                    'Edit',
                    style: TextStyle(
                      fontSize: 10,
                      color: AppThemes.warningColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              // Debts Action with Label
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppThemes.successColor.withValues(alpha: 0.1),
                            AppThemes.successColor.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: AppThemes.successColor.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.account_balance_wallet_rounded, size: 18, color: AppThemes.successColor),
                    ),
                    onPressed: () => _navigateToCustomerDebts(context, customer.customerId),
                    tooltip: 'View Debts',
                  ),
                  Text(
                    'Debts',
                    style: TextStyle(
                      fontSize: 10,
                      color: AppThemes.successColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              // Delete Action with Label
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                            Theme.of(context).colorScheme.error.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(Icons.delete_rounded, size: 18, color: Theme.of(context).colorScheme.error),
                    ),
                    onPressed: () => _navigateToDeleteCustomer(context, customer.customerId),
                    tooltip: 'Delete Customer',
                  ),
                  Text(
                    'Delete',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.error,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  /// Build pagination controls
  Widget _buildPaginationControls(BuildContext context, CustomerListLoaded state) {
    final pagination = state.response.data.pagination;
    final currentPage = pagination.currentPage;
    final totalPages = pagination.totalPages;
    final hasNextPage = pagination.hasNextPage;
    final hasPreviousPage = currentPage > 1;

    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Page info
          Expanded(
            child: Text(
              'Page $currentPage of $totalPages',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          
          // Previous button
          IconButton(
            onPressed: hasPreviousPage ? () => _loadPage(currentPage - 1) : null,
            icon: Icon(
              Icons.chevron_left_rounded,
              color: hasPreviousPage 
                ? Theme.of(context).colorScheme.primary 
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            style: IconButton.styleFrom(
              backgroundColor: hasPreviousPage 
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Page numbers (show current and nearby pages)
          ..._buildPageNumbers(context, currentPage, totalPages),
          
          const SizedBox(width: 8),
          
          // Next button
          IconButton(
            onPressed: hasNextPage ? () => _loadPage(currentPage + 1) : null,
            icon: Icon(
              Icons.chevron_right_rounded,
              color: hasNextPage 
                ? Theme.of(context).colorScheme.primary 
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            style: IconButton.styleFrom(
              backgroundColor: hasNextPage 
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ],
      ),
    );
  }

  /// Build page number buttons
  List<Widget> _buildPageNumbers(BuildContext context, int currentPage, int totalPages) {
    List<Widget> pageButtons = [];
    
    // Show max 5 page numbers
    int startPage = (currentPage - 2).clamp(1, totalPages);
    int endPage = (currentPage + 2).clamp(1, totalPages);
    
    // Adjust range to always show 5 pages if possible
    if (endPage - startPage < 4) {
      if (startPage == 1) {
        endPage = (startPage + 4).clamp(1, totalPages);
      } else {
        startPage = (endPage - 4).clamp(1, totalPages);
      }
    }
    
    for (int page = startPage; page <= endPage; page++) {
      final isCurrentPage = page == currentPage;
      
      pageButtons.add(
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          child: InkWell(
            onTap: isCurrentPage ? null : () => _loadPage(page),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: isCurrentPage 
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surface,
                border: Border.all(
                  color: isCurrentPage
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).dividerColor.withValues(alpha: 0.3),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$page',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isCurrentPage 
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
                  fontWeight: isCurrentPage ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ),
        ),
      );
    }
    
    return pageButtons;
  }

  /// Load specific page
  void _loadPage(int page) {
    final searchQuery = _searchController.text.isNotEmpty ? _searchController.text : null;
    context.read<CustomerBloc>().add(LoadCustomers(page: page, search: searchQuery));
  }

  void _onSearchChanged(String query) {
    context.read<CustomerBloc>().add(
          LoadCustomers(search: query.isNotEmpty ? query : null),
        );
  }

  void _showFilterSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const CustomerFilterSheet(),
    );
  }

  void _navigateToCreateCustomer(BuildContext context) {
    CustomerModalHandlers.showAddCustomerModal(context);
    // The refresh will be handled by the state listener when CustomerCreated is emitted
  }

  void _navigateToCustomerDetails(BuildContext context, String customerId) {
    // Show customer details modal directly
    CustomerModalHandlers.showViewCustomerByIdModal(context, customerId);
  }

  void _navigateToEditCustomer(BuildContext context, String customerId) {
    // Show edit customer modal directly
    CustomerModalHandlers.showEditCustomerByIdModal(context, customerId);
    // The refresh will be handled by the state listener when CustomerUpdated is emitted
  }

  void _navigateToCustomerDebts(BuildContext context, String customerId) {
    // Navigate to debt list filtered by customer
    Navigator.pushNamed(
      context,
      '/debts',
      arguments: {'customerId': customerId},
    ).catchError((error) {
      // Fallback if named route doesn't work
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening customer debts for: $customerId'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          action: SnackBarAction(
            label: 'OK',
            onPressed: () {},
          ),
        ),
      );
    });
  }

  void _navigateToDeleteCustomer(BuildContext context, String customerId) {
    // Find the customer from the current loaded list
    final bloc = context.read<CustomerBloc>();
    final state = bloc.state;
    if (state is CustomerListLoaded) {
      final customers = state.response.data.customers ?? [];
      final customer = customers.firstWhere(
        (c) => c.customerId == customerId,
        orElse: () => throw Exception('Customer not found'),
      );
      
      // Show initial confirmation dialog
      _showDeleteConfirmationDialog(context, customer, bloc);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Customer data not available. Please refresh the list.'),
          backgroundColor: AppThemes.errorColor,
        ),
      );
    }
  }

  void _showDeleteConfirmationDialog(BuildContext context, customer, CustomerBloc bloc) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Theme.of(context).colorScheme.error),
            const SizedBox(width: 8),
            Text('Delete Customer', style: TextStyle(color: Theme.of(context).colorScheme.error)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to delete this customer?'),
            const SizedBox(height: 8),
            Text('Customer: ${customer.customerName}', style: const TextStyle(fontWeight: FontWeight.bold)),
            Text('ID: ${customer.customerId}'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppThemes.warningColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
              ),
              child: Text(
                '⚠️ This action cannot be undone. All customer data will be permanently deleted.',
                style: TextStyle(color: AppThemes.warningColor, fontSize: 12),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              _attemptDeleteCustomer(context, customer.customerId, bloc, false);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppThemes.errorColor),
            child: Text('Delete', style: TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
          ),
        ],
      ),
    );
  }

  void _attemptDeleteCustomer(BuildContext context, String customerId, CustomerBloc bloc, bool force) {
    // Listen for the delete result
    final subscription = bloc.stream.listen((state) {
      if (state is CustomerError) {
        // Handle specific error cases that require force parameter
        if (state.errorCode == 'customer_has_debt_history') {
          _showForceDeleteDialog(context, customerId, bloc);
        } else if (state.errorCode == 'customer_has_active_debts') {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppThemes.errorColor,
              action: SnackBarAction(
                label: 'OK',
                onPressed: () {},
              ),
            ),
          );
        } else if (state.errorCode == 'recent_activity_exists') {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppThemes.warningColor,
              action: SnackBarAction(
                label: 'OK',
                onPressed: () {},
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppThemes.errorColor,
            ),
          );
        }
      } else if (state is CustomerDeleted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Customer deleted successfully'),
            backgroundColor: AppThemes.successColor,
          ),
        );
        // Refresh the customer list
        bloc.add(const RefreshCustomers());
      }
    });

    // Trigger the delete
    bloc.add(DeleteCustomer(customerId, force: force));

    // Clean up subscription after a delay
    Future.delayed(const Duration(seconds: 5), () {
      subscription.cancel();
    });
  }

  void _showForceDeleteDialog(BuildContext context, String customerId, CustomerBloc bloc) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.history, color: AppThemes.warningColor),
            const SizedBox(width: 8),
            Text('Customer Has Debt History', style: TextStyle(color: AppThemes.warningColor)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('This customer has debt history in the system.'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ℹ️ What happens when you force delete:',
                    style: TextStyle(color: Theme.of(context).colorScheme.primary, fontWeight: FontWeight.bold, fontSize: 12),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• Customer record will be soft-deleted\n• Debt records will be preserved for audit\n• Historical data remains intact',
                    style: TextStyle(color: Theme.of(context).colorScheme.primary, fontSize: 11),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              _attemptDeleteCustomer(context, customerId, bloc, true);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppThemes.warningColor),
            child: Text('Force Delete', style: TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
          ),
        ],
      ),
    );
  }
} 