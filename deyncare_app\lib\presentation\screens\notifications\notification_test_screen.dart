import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/notification/notification_bloc.dart';

class NotificationTestScreen extends StatelessWidget {
  const NotificationTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔔 Notification Test'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocListener<NotificationBloc, NotificationState>(
        listener: (context, state) {
          if (state is NotificationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('❌ ${state.message}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          } else if (state is NotificationTokenRegistered) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('✅ ${state.message}'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          } else if (state is NotificationTestSent) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('✅ ${state.message}'),
                backgroundColor: Colors.blue,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        },
        child: BlocBuilder<NotificationBloc, NotificationState>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Firebase Status Card
                  _buildStatusCard(context),
                  
                  const SizedBox(height: 16),
                  
                  // Action Buttons
                  _buildActionButtons(context, state),
                  
                  const SizedBox(height: 16),
                  
                  // Notification History
                  _buildNotificationHistory(context),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatusCard(BuildContext context) {
    final notificationBloc = context.read<NotificationBloc>();
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🔥 Firebase Status',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildStatusRow(
              '🚀 Firebase Initialized', 
              notificationBloc.isFirebaseInitialized,
            ),
            _buildStatusRow(
              '🔑 FCM Token Generated', 
              notificationBloc.fcmToken != null,
            ),
            _buildStatusRow(
              '📝 Token Registered', 
              notificationBloc.isTokenRegistered,
            ),
            const SizedBox(height: 12),
            if (notificationBloc.fcmToken != null) ...[
              Text(
                '🔑 FCM Token:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  notificationBloc.fcmToken!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
            const SizedBox(height: 12),
            Text(
              '📱 Device Info:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            ...notificationBloc.deviceInfo.entries.map(
              (entry) => Text('${entry.key}: ${entry.value}'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool isActive) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isActive ? Icons.check_circle : Icons.error,
            color: isActive ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            isActive ? 'Active' : 'Inactive',
            style: TextStyle(
              color: isActive ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, NotificationState state) {
    final isLoading = state is NotificationLoading;
    final notificationBloc = context.read<NotificationBloc>();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          '🧪 Test Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        // Initialize Notifications Button
        ElevatedButton.icon(
          onPressed: isLoading ? null : () {
            notificationBloc.add(InitializeNotifications());
          },
          icon: const Icon(Icons.rocket_launch),
          label: const Text('Initialize Notifications'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Register FCM Token Button
        ElevatedButton.icon(
          onPressed: (isLoading || !notificationBloc.isFirebaseInitialized) 
              ? null 
              : () {
                  notificationBloc.add(RegisterFCMToken());
                },
          icon: const Icon(Icons.app_registration),
          label: const Text('Register FCM Token'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Send Test Notification Button
        ElevatedButton.icon(
          onPressed: (isLoading || !notificationBloc.isTokenRegistered) 
              ? null 
              : () {
                  notificationBloc.add(SendTestNotification());
                },
          icon: const Icon(Icons.send),
          label: const Text('Send Test Notification'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Clear History Button
        ElevatedButton.icon(
          onPressed: isLoading ? null : () {
            notificationBloc.add(ClearNotificationHistory());
          },
          icon: const Icon(Icons.clear_all),
          label: const Text('Clear History'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        
        if (isLoading) ...[
          const SizedBox(height: 16),
          const Center(
            child: CircularProgressIndicator(),
          ),
        ],
      ],
    );
  }

  Widget _buildNotificationHistory(BuildContext context) {
    final notificationBloc = context.read<NotificationBloc>();
    final history = notificationBloc.notificationHistory;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📋 Notification History (${history.length})',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        if (history.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  '📭 No notifications received yet\n\nTry sending a test notification!',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ),
          )
        else
          ...history.map((notification) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: _getNotificationColor(notification['type']),
                child: Text(
                  _getNotificationIcon(notification['type']),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              title: Text(
                notification['title'] ?? 'No title',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(notification['body'] ?? 'No body'),
                  const SizedBox(height: 4),
                  Text(
                    'Received: ${notification['receivedAt'] ?? 'Unknown'}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              trailing: Chip(
                label: Text(
                  notification['type'] ?? 'unknown',
                  style: const TextStyle(fontSize: 12),
                ),
                backgroundColor: _getNotificationColor(notification['type']).withOpacity(0.1),
              ),
            ),
          )),
      ],
    );
  }

  Color _getNotificationColor(String? type) {
    switch (type) {
      case 'debt_created':
        return Colors.red;
      case 'payment_recorded':
        return Colors.green;
      case 'debt_reminder':
        return Colors.orange;
      case 'test':
        return Colors.blue;
      case 'custom':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getNotificationIcon(String? type) {
    switch (type) {
      case 'debt_created':
        return '💰';
      case 'payment_recorded':
        return '💳';
      case 'debt_reminder':
        return '⏰';
      case 'test':
        return '🧪';
      case 'custom':
        return '📢';
      default:
        return '📋';
    }
  }
} 