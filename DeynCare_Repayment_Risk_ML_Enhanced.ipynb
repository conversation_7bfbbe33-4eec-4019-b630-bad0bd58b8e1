{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeynCare Repayment Risk Model - Complete ML Pipeline\n", "\n", "## 🎯 1. Problem Definition / Objective\n", "\n", "### **Business Problem**\n", "- **Challenge**: Debt collection issues and need for ownership decisions based on repayment history\n", "- **Goal**: Build ML-powered risk assessment system for mobile app + Node.js backend\n", "- **Integration**: Deploy via FastAPI for real-time risk scoring\n", "\n", "### **Technical Objectives**\n", "- ✅ Predict probability of on-time repayment\n", "- ✅ Output risk levels: Low, Medium, High  \n", "- ✅ Support automated business decisions\n", "- ✅ Learn from historical payment patterns\n", "- ✅ Integrate with existing mobile app ecosystem\n", "\n", "### **Success Metrics**\n", "- **Accuracy**: >80% prediction accuracy\n", "- **Business Impact**: Reduce default rate by 15%\n", "- **Speed**: <100ms response time for real-time scoring\n", "- **Explainability**: Clear feature importance for business decisions"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 📥 2. Data Collection\n", "\n", "### **Data Source**\n", "- **Origin**: Synthetic data generated from mobile app + Node.js backend\n", "- **Collection Method**: Historical transaction and repayment data\n", "- **Integration**: Real-time data pipeline from mobile app to ML system\n", "\n", "### **Data Schema**\n", "| Field | Type | Description | Business Logic |\n", "|-------|------|-------------|----------------|\n", "| `CustomerID` | String | Unique customer identifier | Links to user profiles |\n", "| `CustomerType` | Categorical | New/Returning customer | Returning = lower risk |\n", "| `DebtAmount` | Numeric | Original debt amount | Higher amount = higher risk |\n", "| `DebtPaidRatio` | Numeric | Paid/Total ratio | Higher ratio = lower risk |\n", "| `PaymentDelay` | Numeric | Days early/late | Negative = early, positive = late |\n", "| `OutstandingDebt` | Numeric | Remaining balance | Higher balance = higher risk |\n", "| `IsOnTime` | Target | Payment status | 1=On-time, 0=Late |\n", "\n", "### **Data Quality Expectations**\n", "- **Volume**: 500+ historical records for initial training\n", "- **Freshness**: Updated daily from mobile app transactions\n", "- **Completeness**: <5% missing values acceptable\n", "- **Accuracy**: Validated against mobile app payment confirmations\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, classification_report, roc_auc_score\n", "from xgboost import XGBClassifier\n", "import joblib\n", "import os\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (500, 15)\n", "\n", "First 5 rows:\n", "  CustomerID CustomerName CustomerType DebtID  DebtAmount  RepaymentTime  \\\n", "0       C001        Hodan          New   D001        2033              1   \n", "1       C002       <PERSON><PERSON>na    Returning   D002        2395             43   \n", "2       C003        <PERSON><PERSON><PERSON>    Returning   D003        2682             63   \n", "3       C004       Halima          New   D004        2306             21   \n", "4       C005        Zahra          New   D005        2582            116   \n", "\n", "  DebtCreationDate     DueDate  PaidAmount  OutstandingDebt    PaidDate  \\\n", "0       12/19/2024  12/20/2024        1781              252  12/27/2024   \n", "1       12/19/2024   1/31/2025         658             1737    2/4/2025   \n", "2       12/19/2024   2/20/2025        1793              889   2/18/2025   \n", "3       12/19/2024    1/9/2025        1886              420    1/5/2025   \n", "4       12/19/2024   4/14/2025        2280              302   4/12/2025   \n", "\n", "   DebtPaidRatio  DaysUntilDue  PaymentDelay IsOnTime  \n", "0           0.88             1             7       No  \n", "1           0.27            43             4       No  \n", "2           0.67            63            -2      Yes  \n", "3           0.82            21            -4      Yes  \n", "4           0.88           116            -2      Yes  \n"]}], "source": ["# Load data\n", "df = pd.read_csv('DeynCare_Repayment_Risk_ML_Data_set.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(\"\\nFirst 5 rows:\")\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features: ['DebtPaidRatio', 'PaymentDelay', 'OutstandingDebt', 'DebtAmount', 'CustomerType']\n", "Target distribution: IsOnTime\n", "0    262\n", "1    238\n", "Name: count, dtype: int64\n", "\n", "Processed data with encoding:\n", "  CustomerID CustomerName  CustomerType DebtID  DebtAmount  RepaymentTime  \\\n", "0       C001        Hodan             0   D001        2033              1   \n", "1       C002       Aamina             1   D002        2395             43   \n", "2       C003        Aadan             1   D003        2682             63   \n", "3       C004       Halima             0   D004        2306             21   \n", "4       C005        Zahra             0   D005        2582            116   \n", "\n", "  DebtCreationDate     DueDate  PaidAmount  OutstandingDebt    PaidDate  \\\n", "0       12/19/2024  12/20/2024        1781              252  12/27/2024   \n", "1       12/19/2024   1/31/2025         658             1737    2/4/2025   \n", "2       12/19/2024   2/20/2025        1793              889   2/18/2025   \n", "3       12/19/2024    1/9/2025        1886              420    1/5/2025   \n", "4       12/19/2024   4/14/2025        2280              302   4/12/2025   \n", "\n", "   DebtPaidRatio  DaysUntilDue  PaymentDelay  IsOnTime  \n", "0           0.88             1             7         0  \n", "1           0.27            43             4         0  \n", "2           0.67            63            -2         1  \n", "3           0.82            21            -4         1  \n", "4           0.88           116            -2         1  \n"]}], "source": ["# Data preprocessing\n", "df_processed = df.copy()\n", "\n", "# Encode categorical variables\n", "df_processed[\"CustomerType\"] = df_processed[\"CustomerType\"].map({\"New\": 0, \"Returning\": 1})\n", "df_processed[\"IsOnTime\"] = df_processed[\"IsOnTime\"].map({\"No\": 0, \"Yes\": 1})\n", "\n", "# Select features (excluding IsOnTime to prevent data leakage)\n", "features = ['DebtPaidRatio', 'PaymentDelay', 'OutstandingDebt', 'DebtAmount', 'CustomerType']\n", "X = df_processed[features]\n", "y = df_processed['IsOnTime']\n", "\n", "print(f\"Features: {features}\")\n", "print(f\"Target distribution: {y.value_counts()}\")\n", "\n", "# Check the processed data (with encoding applied)\n", "print(\"\\nProcessed data with encoding:\")\n", "print(df_processed.head())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  CustomerID CustomerName  CustomerType DebtID  DebtAmount  RepaymentTime  \\\n", "0       C001        Hodan             0   D001        2033              1   \n", "1       C002       Aamina             1   D002        2395             43   \n", "2       C003        Aadan             1   D003        2682             63   \n", "3       C004       Halima             0   D004        2306             21   \n", "4       C005        Zahra             0   D005        2582            116   \n", "\n", "  DebtCreationDate     DueDate  PaidAmount  OutstandingDebt    PaidDate  \\\n", "0       12/19/2024  12/20/2024        1781              252  12/27/2024   \n", "1       12/19/2024   1/31/2025         658             1737    2/4/2025   \n", "2       12/19/2024   2/20/2025        1793              889   2/18/2025   \n", "3       12/19/2024    1/9/2025        1886              420    1/5/2025   \n", "4       12/19/2024   4/14/2025        2280              302   4/12/2025   \n", "\n", "   DebtPaidRatio  DaysUntilDue  PaymentDelay  IsOnTime  \n", "0           0.88             1             7         0  \n", "1           0.27            43             4         0  \n", "2           0.67            63            -2         1  \n", "3           0.82            21            -4         1  \n", "4           0.88           116            -2         1  \n"]}], "source": ["print(df_processed.head())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training set: (400, 5)\n", "Test set: (100, 5)\n"]}], "source": ["# Split and scale data\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"Training set: {X_train.shape}\")\n", "print(f\"Test set: {X_test.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 6. Model Selection & Training\n", "\n", "### **Model Strategy for Mobile App Integration**\n", "We'll evaluate three models optimized for real-time mobile app deployment:\n", "\n", "1. **Logistic Regression** (baseline) - Fast inference, interpretable\n", "2. **Random Forest** - <PERSON><PERSON>, handles non-linear patterns\n", "3. **XGBoost** - High performance, feature importance\n", "\n", "### **Evaluation Criteria**\n", "- **Accuracy** (business impact)\n", "- **AUC-ROC** (model discrimination)\n", "- **Precision/Recall** (debt collection efficiency)\n", "- **Inference Speed** (mobile app responsiveness)\n", "- **Feature Importance** (business explainability)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Training Logistic Regression...\n", "AUC-ROC: 1.0000\n", "Accuracy: 1.0000\n", "\n", "Training Random Forest...\n", "AUC-ROC: 1.0000\n", "Accuracy: 1.0000\n", "\n", "Training XGBoost...\n", "AUC-ROC: 1.0000\n", "Accuracy: 1.0000\n"]}], "source": ["# Train models\n", "models = {\n", "    'Logistic Regression': LogisticRegression(random_state=42),\n", "    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),\n", "    'XGBoost': XGBClassifier(n_estimators=100, random_state=42, eval_metric='logloss')\n", "}\n", "\n", "results = {}\n", "\n", "for name, model in models.items():\n", "    print(f\"\\nTraining {name}...\")\n", "    model.fit(X_train_scaled, y_train)\n", "    \n", "    y_pred = model.predict(X_test_scaled)\n", "    y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]\n", "    \n", "    auc = roc_auc_score(y_test, y_pred_proba)\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    \n", "    results[name] = {\n", "        'model': model,\n", "        'auc': auc,\n", "        'accuracy': accuracy,\n", "        'predictions': y_pred,\n", "        'probabilities': y_pred_proba\n", "    }\n", "    \n", "    print(f\"AUC-ROC: {auc:.4f}\")\n", "    print(f\"Accuracy: {accuracy:.4f}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best Model: Logistic Regression\n", "AUC-ROC: 1.0000\n", "Accuracy: 1.0000\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       1.00      1.00      1.00        52\n", "           1       1.00      1.00      1.00        48\n", "\n", "    accuracy                           1.00       100\n", "   macro avg       1.00      1.00      1.00       100\n", "weighted avg       1.00      1.00      1.00       100\n", "\n"]}], "source": ["# Model evaluation and selection\n", "best_model_name = max(results, key=lambda k: results[k]['auc'])\n", "best_model = results[best_model_name]['model']\n", "best_results = results[best_model_name]\n", "\n", "print(f\"Best Model: {best_model_name}\")\n", "print(f\"AUC-ROC: {best_results['auc']:.4f}\")\n", "print(f\"Accuracy: {best_results['accuracy']:.4f}\")\n", "\n", "# Classification report\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, best_results['predictions']))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Risk Level Mapping\n", "Map probability scores to risk levels:\n", "- ≤ 0.3: Low Risk\n", "- 0.3–0.6: Medium Risk\n", "- > 0.6: High Risk"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Risk Level Distribution:\n", "High Risk: 48 (48.0%)\n", "Low Risk: 47 (47.0%)\n", "Medium Risk: 5 (5.0%)\n"]}], "source": ["# Risk level mapping\n", "def map_to_risk_level(probability):\n", "    if probability <= 0.3:\n", "        return 'Low'\n", "    elif probability <= 0.6:\n", "        return 'Medium'\n", "    else:\n", "        return 'High'\n", "\n", "# Apply risk mapping\n", "y_pred_proba = best_results['probabilities']\n", "risk_levels = [map_to_risk_level(p) for p in y_pred_proba]\n", "\n", "# Risk distribution\n", "risk_dist = pd.Series(risk_levels).value_counts()\n", "print(\"Risk Level Distribution:\")\n", "for level, count in risk_dist.items():\n", "    pct = count / len(risk_levels) * 100\n", "    print(f\"{level} Risk: {count} ({pct:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Model Coefficients:\n", "           Feature  Coefficient\n", "1     PaymentDelay    -5.831333\n", "3       DebtAmount     0.323215\n", "2  OutstandingDebt    -0.285115\n", "0    DebtPaidRatio    -0.175294\n", "4     CustomerType     0.010862\n"]}], "source": ["# Feature importance (if available)\n", "if hasattr(best_model, 'feature_importances_'):\n", "    feature_importance = pd.DataFrame({\n", "        'Feature': features,\n", "        'Importance': best_model.feature_importances_\n", "    }).sort_values('Importance', ascending=False)\n", "    \n", "    print(\"\\nFeature Importance:\")\n", "    print(feature_importance)\n", "    \n", "<PERSON><PERSON>(best_model, 'coef_'):\n", "    coef_df = pd.DataFrame({\n", "        'Feature': features,\n", "        'Coefficient': best_model.coef_[0]\n", "    }).sort_values('Coefficient', ascending=False, key=abs)\n", "    \n", "    print(\"\\nModel Coefficients:\")\n", "    print(coef_df)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Model saved to 'models/repayment_risk_model.pkl'\n", "✅ Feature names saved to 'models/feature_names.txt'\n", "\n", "🎯 Model Training Complete!\n", "📊 Best Model: Logistic Regression\n", "📈 AUC-ROC: 1.0000\n", "🎯 Accuracy: 1.0000\n", "📁 Model artifacts saved in 'models/' directory\n"]}], "source": ["# Save model artifacts\n", "os.makedirs('models', exist_ok=True)\n", "\n", "model_artifacts = {\n", "    'model': best_model,\n", "    'scaler': scaler,\n", "    'feature_names': features,\n", "    'model_name': best_model_name,\n", "    'performance': {\n", "        'auc_roc': best_results['auc'],\n", "        'accuracy': best_results['accuracy']\n", "    },\n", "    'risk_mapping': {\n", "        'low': '≤ 0.3',\n", "        'medium': '0.3 - 0.6',\n", "        'high': '> 0.6'\n", "    }\n", "}\n", "\n", "# Save model\n", "joblib.dump(model_artifacts, 'models/repayment_risk_model.pkl')\n", "print(\"✅ Model saved to 'models/repayment_risk_model.pkl'\")\n", "\n", "# Save feature names for API\n", "with open('models/feature_names.txt', 'w') as f:\n", "    f.write('\\n'.join(features))\n", "print(\"✅ Feature names saved to 'models/feature_names.txt'\")\n", "\n", "print(f\"\\n🎯 Model Training Complete!\")\n", "print(f\"📊 Best Model: {best_model_name}\")\n", "print(f\"📈 AUC-ROC: {best_results['auc']:.4f}\")\n", "print(f\"🎯 Accuracy: {best_results['accuracy']:.4f}\")\n", "print(f\"📁 Model artifacts saved in 'models/' directory\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['.ipynb_checkpoints', 'feature_names.txt', 'repayment_risk_model.pkl', 'Untitled.ipynb']\n"]}], "source": ["import os\n", "print(os.listdir('models'))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking saved model files:\n", "Model file exists: True\n", "Feature names exist: True\n"]}], "source": ["# Import libraries for testing\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "import os\n", "\n", "# Check if model files exist\n", "print(\"Checking saved model files:\")\n", "print(f\"Model file exists: {os.path.exists('models/repayment_risk_model.pkl')}\")\n", "print(f\"Feature names exist: {os.path.exists('models/feature_names.txt')}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking saved model files:\n", "Model file exists: True\n", "Feature names exist: True\n"]}], "source": ["# Import libraries for testing\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "import os\n", "\n", "# Check if model files exist\n", "print(\"Checking saved model files:\")\n", "print(f\"Model file exists: {os.path.exists('models/repayment_risk_model.pkl')}\")\n", "print(f\"Feature names exist: {os.path.exists('models/feature_names.txt')}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded successfully!\n", "Model type: Logistic Regression\n", "Features: ['DebtPaidRatio', 'PaymentDelay', 'OutstandingDebt', 'DebtAmount', 'CustomerType']\n", "Performance: AUC-ROC = 1.0000\n", "Risk mapping: {'low': '≤ 0.3', 'medium': '0.3 - 0.6', 'high': '> 0.6'}\n"]}], "source": ["# Load saved model artifacts\n", "model_artifacts = joblib.load('models/repayment_risk_model.pkl')\n", "\n", "print(\"Model loaded successfully!\")\n", "print(f\"Model type: {model_artifacts['model_name']}\")\n", "print(f\"Features: {model_artifacts['feature_names']}\")\n", "print(f\"Performance: AUC-ROC = {model_artifacts['performance']['auc_roc']:.4f}\")\n", "print(f\"Risk mapping: {model_artifacts['risk_mapping']}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model components extracted:\n", "Model: <class 'sklearn.linear_model._logistic.LogisticRegression'>\n", "Scaler: <class 'sklearn.preprocessing._data.StandardScaler'>\n", "Features: ['DebtPaidRatio', 'PaymentDelay', 'OutstandingDebt', 'DebtAmount', 'CustomerType']\n"]}], "source": ["# Extract components\n", "model = model_artifacts['model']\n", "scaler = model_artifacts['scaler']\n", "feature_names = model_artifacts['feature_names']\n", "\n", "print(\"Model components extracted:\")\n", "print(f\"Model: {type(model)}\")\n", "print(f\"Scaler: {type(scaler)}\")\n", "print(f\"Features: {feature_names}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample data for testing:\n", "   DebtPaidRatio  PaymentDelay  OutstandingDebt  DebtAmount  CustomerType\n", "0           0.95            -3              100        2000             1\n", "1           0.40            12             1600        2000             0\n", "2           0.70             0              600        2000             1\n", "3           0.55             5              900        2000             0\n", "4           0.85            -1              300        2000             1\n", "\n", "Predictions:\n", "Sample 1: Probability=0.995, Risk Level=Low\n", "Sample 2: Probability=0.000, Risk Level=High\n", "Sample 3: Probability=0.688, Risk Level=Medium\n", "Sample 4: Probability=0.001, Risk Level=High\n", "Sample 5: Probability=0.912, Risk Level=Low\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but StandardScaler was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but StandardScaler was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but StandardScaler was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but StandardScaler was fitted with feature names\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but StandardScaler was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "# Create sample data for core features\n", "sample_data = pd.DataFrame([\n", "    # DebtPaid<PERSON>atio, Payment<PERSON>elay, OutstandingDebt, DebtAmount, CustomerType\n", "    [0.95, -3, 100, 2000, 1],   # Very good, returning customer, early payment\n", "    [0.40, 12, 1600, 2000, 0],  # Risky, new customer, late payment\n", "    [0.70, 0, 600, 2000, 1],    # On-time, returning customer\n", "    [0.55, 5, 900, 2000, 0],    # Slightly late, new customer\n", "    [0.85, -1, 300, 2000, 1],   # Good, returning, early payment\n", "], columns=['DebtPaidRatio', 'PaymentDelay', 'OutstandingDebt', 'DebtAmount', 'CustomerType'])\n", "\n", "print(\"Sample data for testing:\")\n", "print(sample_data)\n", "\n", "# Function to predict risk for a single row\n", "def predict_risk_row(row):\n", "    input_data = np.array([row])\n", "    input_scaled = scaler.transform(input_data)\n", "    probability = model.predict_proba(input_scaled)[0][1]\n", "    # Corrected risk mapping: high probability = low risk\n", "    if probability >= 0.7:\n", "        risk_level = 'Low'\n", "    elif probability >= 0.4:\n", "        risk_level = 'Medium'\n", "    else:\n", "        risk_level = 'High'\n", "    return probability, risk_level\n", "\n", "print(\"\\nPredictions:\")\n", "for idx, row in sample_data.iterrows():\n", "    prob, risk = predict_risk_row(row)\n", "    print(f\"Sample {idx+1}: Probability={prob:.3f}, Risk Level={risk}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}