import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:logger/logger.dart';

/// Remote data source for authentication operations
/// 
/// Handles all API interactions with the backend auth endpoints
class AuthRemoteSource {
  final Logger _logger = Logger();
  final DioClient _dioClient;

  /// Creates a new instance with the required client
  AuthRemoteSource({
    DioClient? dioClient,
  }) : _dioClient = dioClient ?? DioClient();

  /// Login with email and password
  Future<Map<String, dynamic>> login(String email, String password) async {
    // Important: Disable retry for login attempts to prevent stuck loading state on auth failures
    final response = await _dioClient.post(
      '/auth/login',
      data: {
        'email': email,
        'password': password,
        'deviceName': 'DeynCare Mobile App',
      },
      // Disable retry for login to prevent stuck loading state
      retry: false,
      // Don't show automatic success toast, we'll handle it in the UI
      showSuccessToast: false,
    );
    
    _validateResponse(response);
    
    // Extract data from response
    final data = response['data'];
    
    // Make sure we have a proper data object
    if (data == null || data is! Map<String, dynamic>) {
      throw ApiException(
        message: 'Invalid response format from server',
        code: 'invalid_response_format',
      );
    }
    
    // Check if we have the user object
    if (!data.containsKey('user') || data['user'] is! Map<String, dynamic>) {
      throw ApiException(
        message: 'User data missing in response',
        code: 'missing_user_data',
      );
    }
    
    // Check for access token
    if (!data.containsKey('accessToken') || data['accessToken'] == null) {
      throw ApiException(
        message: 'Access token missing in response',
        code: 'missing_access_token',
      );
    }
    
    // If refresh token is missing, use the access token as a fallback
    // This ensures compatibility with backends that don't return a separate refresh token
    if (!data.containsKey('refreshToken') || data['refreshToken'] == null) {
      if (kDebugMode) {
        print('Warning: No refresh token in response, using access token as fallback');
      }
      data['refreshToken'] = data['accessToken'];
    }
    
    return data;
  }
  
  /// Register new user with shop (admin)
  Future<Map<String, dynamic>> register({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    File? shopLogo,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  }) async {
    // The new backend API (POST /register/init) handles email existence check internally.
    // So, we can remove the frontend check here.
    
    dynamic response;
    
    try {
      // Handle with or without logo upload
      if (shopLogo != null) {
        // For file upload, we need to use multipart request
        response = await _dioClient.uploadFile(
          '/register/init',
          file: shopLogo,
          fieldName: 'logo',
          data: {
            'fullName': fullName,
            'email': email,
            'phone': phone,
            'password': password,
            'shopName': shopName,
            'shopAddress': shopAddress,
            'planType': planType,
            'paymentMethod': paymentMethod,
            'initialPaid': initialPaid.toString(),
            if (discountCode != null) 'discountCode': discountCode,
          },
        );
      } else {
        // No file upload required
        response = await _dioClient.post(
          '/register/init',
          data: {
            'fullName': fullName,
            'email': email,
            'phone': phone,
            'password': password,
            'shopName': shopName,
            'shopAddress': shopAddress,
            'planType': planType,
            'paymentMethod': paymentMethod,
            'initialPaid': initialPaid.toString(),
            if (discountCode != null) 'discountCode': discountCode,
          },
        );
      }
      
      _validateResponse(response);
      
      // The backend's /register/init response is expected to be:
      // { "success": true, "message": "...", "user": {...}, "registrationProgress": {...} }
      final data = response['data'];
      
      if (data == null || data is! Map<String, dynamic>) {
        throw ApiException(
          message: 'Invalid response format from server for initial registration',
          code: 'invalid_response_format',
        );
      }

      if (!data.containsKey('user') || data['user'] is! Map<String, dynamic>) {
        throw ApiException(
          message: 'User data missing in initial registration response',
          code: 'missing_user_data',
        );
      }
      // After _validateResponse, 'data' is guaranteed to be a Map<String, dynamic>.
      // We just need to check if 'registrationProgress' is missing.
      if (!data.containsKey('registrationProgress')) {
        // If not, create a default one to proceed to email verification.
        data['registrationProgress'] = {
          'nextStep': 'verify_email_required',
          // The backend should provide this, but we add a fallback.
          'verificationCodeExpiresAt': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
        };
      }

      return data; // Return both user and registrationProgress
    } catch (e) {
      // Re-throw any API exceptions
      if (e is ApiException) {
        rethrow;
      }
      // For other errors, wrap them in a generic API exception
      throw ApiException(
        message: 'An unexpected error occurred during registration initiation: ${e.toString()}',
        code: 'unexpected_error',
      );
    }
  }
  
  /// Create employee user (requires admin privileges)
  Future<Map<String, dynamic>> createEmployee({
    required String userTitle,
    required String email,
    required String password,
    required Map<String, dynamic> visibility,
  }) async {
    final response = await _dioClient.post(
      '/auth/create-employee',
      data: {
        'userTitle': userTitle,
        'email': email,
        'password': password,
        'visibility': visibility,
      },
    );
    
    _validateResponse(response);
    return response['data'];
  }
  
  /// Verify email with verification code
  Future<Map<String, dynamic>> verifyEmail(String email, String verificationCode) async {
    _logger.d('AuthRemoteSource: Verifying email: $email');
    
    try {
      final response = await _dioClient.post(
        '/register/verify-email',
        data: {
          'email': email,
          'verificationCode': verificationCode,
        },
        // Disable retry for verification to prevent duplicate requests
        retry: false,
      );
      
      _validateResponse(response);
      
      // Safely cast data to Map<String, dynamic>
      Map<String, dynamic> data = {};
      
      if (response['data'] != null) {
        try {
          if (response['data'] is Map<String, dynamic>) {
            data = response['data'] as Map<String, dynamic>;
          } else if (response['data'] is Map) {
            data = Map<String, dynamic>.from(response['data'] as Map);
          }
        } catch (e) {
          _logger.e('AuthRemoteSource: Error casting verification response data: $e');
          _logger.e('AuthRemoteSource: Response data type: ${response['data'].runtimeType}');
          throw ApiException(
            message: 'Error processing verification response: ${e.toString()}',
            code: 'verification_response_format_error',
          );
        }
      }
      
      _logger.d('AuthRemoteSource: Email verification response received: $data');
      
      // Extract tokens from the response if available
      final accessToken = data['accessToken'];
      final refreshToken = data['refreshToken'];
      
      if (accessToken != null && refreshToken != null) {
        _logger.d('AuthRemoteSource: Tokens received from email verification');
        _logger.d('AuthRemoteSource: Access token length: ${accessToken.toString().length}');
        _logger.d('AuthRemoteSource: Refresh token length: ${refreshToken.toString().length}');
        
        // Update the DioClient with the new token
        _dioClient.updateAuthToken(accessToken);
        _logger.d('AuthRemoteSource: DioClient updated with new token from email verification');
        
        // Add tokens to the user data for easy access
        if (data.containsKey('user') && data['user'] is Map) {
          Map<String, dynamic> userData = {};
          
          try {
            if (data['user'] is Map<String, dynamic>) {
              userData = Map<String, dynamic>.from(data['user'] as Map<String, dynamic>);
            } else if (data['user'] is Map) {
              userData = Map<String, dynamic>.from(data['user'] as Map);
            }
            
            userData['accessToken'] = accessToken;
            userData['refreshToken'] = refreshToken;
            userData['tokenExpiresAt'] = DateTime.now().add(const Duration(hours: 2)).toIso8601String();  // Fixed: Match backend 2h
            
            // Update the user data in the response with the modified copy
            data['user'] = userData;
            _logger.d('AuthRemoteSource: Added token information to user data');
          } catch (e) {
            _logger.e('AuthRemoteSource: Error processing user data: $e');
            // Continue without adding token info to user data
          }
        } else {
          _logger.w('AuthRemoteSource: No tokens received from email verification');
        }
      } else {
        _logger.w('AuthRemoteSource: No tokens received from email verification');
      }
      
      // Format the response for the repository
      final formattedResponse = {
        'user': data['user'] ?? {},
        'registrationProgress': {
          'currentStep': 'email_verified',
          'nextStep': data['nextStep'] ?? 'payment_required',
          'progress': 50,
          'verificationCodeExpiresAt': null,
          'data': {}, // Add empty data object to satisfy the model requirements
        },
        // Pass tokens directly in the response
        'accessToken': accessToken,
        'refreshToken': refreshToken,
      };
      
      _logger.d('AuthRemoteSource: Email verification successful');
      return formattedResponse;
    } catch (e) {
      _logger.e('AuthRemoteSource: Error during email verification: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'An unexpected error occurred during email verification',
        code: 'unexpected_error',
      );
    }
  }
  
  /// Resend verification code
  Future<void> resendVerification(String email) async {
    try {
      final response = await _dioClient.post(
        '/auth/resend-verification',
        data: {
          'email': email.toLowerCase().trim(),
        },
        showSuccessToast: false,
      );
      
      // Log successful resend
      _logger.i('Verification code resent successfully to: $email');
    } catch (e) {
      _logger.e('Failed to resend verification code to: $email', error: e);
      
      // Re-throw with more specific error message
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: 'Failed to resend verification code. Please try again.',
          code: 'resend_verification_failed',
        );
      }
    }
  }
  
  /// Process payment for a subscription plan
  Future<Map<String, dynamic>> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required String phoneNumber,
    required double amount, // Keep for logging but don't send to backend
    String? discountCode,
    // Offline payment fields
    String? payerName,
    String? payerPhone,
    String? notes,
    File? paymentProof,
  }) async {
    try {
      _logger.d('AuthRemoteSource: Processing payment. User: $userId, Shop: $shopId, Plan: $planId');
      
      // Prepare payment data based on payment method
      dynamic requestData;

      if (paymentMethod == 'offline') {
        // For offline payments, use FormData to support file uploads
        requestData = FormData.fromMap({
          'planType': planId,
          'paymentMethod': paymentMethod,
          if (payerName != null && payerName.isNotEmpty) 'payerName': payerName,
          if (payerPhone != null && payerPhone.isNotEmpty) 'payerPhone': payerPhone,
          if (notes != null && notes.isNotEmpty) 'notes': notes,
          if (discountCode != null && discountCode.isNotEmpty) 'discountCode': discountCode,
        });

        // Add payment proof file if provided
        if (paymentProof != null) {
          requestData.files.add(MapEntry(
            'paymentProof',
            await MultipartFile.fromFile(
              paymentProof.path,
              filename: paymentProof.path.split('/').last,
            ),
          ));
          _logger.d('  Payment proof file added: ${paymentProof.path}');
        }

        _logger.d('  Offline payment data prepared with ${requestData.files.length} files');
      } else {
        // For online payments (EVC Plus, etc.), use regular JSON
        requestData = {
          'planType': planId,
          'paymentMethod': paymentMethod,
        };

        // Only add paymentDetails for EVC Plus payments
        if (paymentMethod == 'EVC Plus' && phoneNumber.isNotEmpty) {
          // Format phone number to match backend validation
          String formattedPhone = _formatPhoneForBackend(phoneNumber);
          requestData['paymentDetails'] = {
            'phoneNumber': formattedPhone,
          };
          _logger.d('  Formatted phone number: $phoneNumber -> $formattedPhone');
        }

        // Add discount code if provided
        if (discountCode != null && discountCode.isNotEmpty) {
          requestData['discountCode'] = discountCode;
        }
      }
      
      // DEBUG: Log the exact payment data structure being sent
      _logger.d('🔍 EXACT PAYMENT DATA BEING SENT TO BACKEND:');
      _logger.d('  Plan Type: $planId');
      _logger.d('  Payment Method: $paymentMethod');
      _logger.d('  Phone Number: $phoneNumber');
      _logger.d('  Amount (not sent to backend): $amount');
      _logger.d('  Discount Code: $discountCode');
      if (paymentMethod == 'offline') {
        _logger.d('  Offline Payment Fields:');
        _logger.d('    Payer Name: $payerName');
        _logger.d('    Payer Phone: $payerPhone');
        _logger.d('    Notes: $notes');
        _logger.d('    Payment Proof: ${paymentProof != null ? 'File attached' : 'No file'}');
      }

      _logger.d('AuthRemoteSource: Payment data prepared for $paymentMethod payment');
      
      try {
        // Make the payment request with extended timeout for EVC Plus payments
        final options = Options(
          sendTimeout: const Duration(seconds: 90), // 90 seconds for payment processing
          receiveTimeout: const Duration(seconds: 90), // 90 seconds for payment response
        );
        
        final response = await _dioClient.post(
          '/register/pay', // Correct endpoint that matches backend
          data: requestData,
          options: options,
          retry: false, // Don't retry payment requests
        );
        
        _validateResponse(response);
        
        // Safely convert response data to Map<String, dynamic>
        Map<String, dynamic> formattedResponse = {};
        
        if (response['data'] is Map) {
          try {
            if (response['data'] is Map<String, dynamic>) {
              formattedResponse = response['data'] as Map<String, dynamic>;
            } else {
              formattedResponse = Map<String, dynamic>.from(response['data'] as Map);
            }
          } catch (e) {
            _logger.e('AuthRemoteSource: Error casting payment response data: $e');
            throw ApiException(
              message: 'Error processing payment response: ${e.toString()}',
              code: 'payment_response_format_error',
            );
          }
        }
        
        _logger.d('AuthRemoteSource: Payment processed successfully');
        return formattedResponse;
      } catch (apiError) {
        _logger.e('AuthRemoteSource: Error during payment processing: $apiError');
        
                  // Enhanced error logging for debugging
          if (apiError is ApiException) {
            _logger.e('API Exception Details:');
            _logger.e('  Status Code: ${apiError.statusCode}');
            _logger.e('  Error Code: ${apiError.code}');  
            _logger.e('  Message: ${apiError.message}');
            
            // Special handling for payment in progress (409)
            if (apiError.statusCode == 409 && apiError.message.contains('Payment already in progress')) {
              _logger.w('Payment already in progress - backend is processing previous request');
              throw ApiException(
                message: 'Your payment is being processed. Please wait and do not retry immediately.',
                code: 'payment_in_progress',
                statusCode: 409,
              );
            }
            
            // If it's a 400 error, log the request data for debugging
            if (apiError.statusCode == 400) {
              _logger.e('  Request that failed: $requestData');
              _logger.e('  This might be a validation error - check backend logs');
            }
            
            rethrow;
          }
        
        // Wrap other errors
        throw ApiException(
          message: 'Payment processing failed: ${apiError.toString()}',
          code: 'payment_processing_error',
        );
      }
    } catch (e) {
      _logger.e('AuthRemoteSource: Error during payment processing: $e');
      if (e is ApiException) {
        _logger.e('AuthRemoteSource: API Exception details - code: ${e.code}, statusCode: ${e.statusCode}, message: ${e.message}');
        rethrow;
      }
      throw ApiException(
        message: 'An unexpected error occurred during payment processing: ${e.toString()}',
        code: 'unexpected_error',
      );
    }
  }
  
  /// Check if an email already exists in the system
  Future<Map<String, dynamic>> checkEmailExists(String email) async {
    try {
      // Use a custom options object with shorter timeout
      final options = Options(
        sendTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 5),
      );
      
      final response = await _dioClient.post(
        '/auth/check-email',
        data: {
          'email': email,
        },
        // Don't show success toast for this operation
        showSuccessToast: false,
        options: options,
      );
      
      _validateResponse(response);
      return response['data'] ?? {'exists': false};
    } catch (e) {
      // If the error is a 409 Conflict, it means the email exists
      if (e is ApiException && e.statusCode == 409) {
        return {'exists': true};
      }
      // Otherwise rethrow the error
      rethrow;
    }
  }
  
  /// Request password reset
  Future<Map<String, dynamic>> forgotPassword(String email) async {
    final response = await _dioClient.post(
      '/auth/forgot-password',
      data: {
        'email': email,
        'platform': 'mobile',
      },
      // Add specific timeout for forgot password to prevent hanging
      options: Options(
        sendTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ),
      // Disable retry for auth operations to prevent stuck loading
      retry: false,
      // Don't show automatic success toast
      showSuccessToast: false,
    );
    
    _validateResponse(response);
    return response['data'] ?? {};
  }
  
  /// Reset password with token
  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    final response = await _dioClient.post(
      '/auth/reset-password',
      data: {
        'token': token,
        'newPassword': newPassword,
        'confirmPassword': confirmPassword,
      },
    );
    
    _validateResponse(response);
    return response['data'] ?? {};
  }
  
  /// Logout current user
  Future<void> logout() async {
    await _dioClient.post('/auth/logout');
  }
  
  /// Logout from all devices
  Future<void> logoutAll() async {
    await _dioClient.post('/auth/logout-all');
  }
  
  /// Refresh token
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    final response = await _dioClient.post(
      '/auth/refresh-token',
      data: {
        'refreshToken': refreshToken,
      },
    );
    
    _validateResponse(response);
    return response['data'];
  }
  
  /// Change password while logged in
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    final response = await _dioClient.post(
      '/auth/change-password',
      data: {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
        'confirmPassword': confirmPassword,
      },
    );
    
    _validateResponse(response);
    return response['data'] ?? {};
  }
  
  /// Get user profile
  Future<Map<String, dynamic>> getProfile() async {
    final response = await _dioClient.get('/auth/me');
    
    _validateResponse(response);
    return response['data'];
  }

  /// Fetch available payment methods based on context
  Future<List<String>> getAvailablePaymentMethods(String context) async {
    try {
      final response = await _dioClient.get(
        '/settings/payment-methods',
        queryParameters: {'context': context},
        // It's a public endpoint, usually doesn't need auth, but DioClient might add it.
        // No need to show success toast for this background fetch.
        showSuccessToast: false, 
      );

      _validateResponse(response);

      if (response['data'] != null && response['data']['paymentMethods'] is List) {
        // Ensure the list elements are strings
        return List<String>.from(response['data']['paymentMethods']);
      } else {
        // Return empty list or throw error if data is not as expected
        if (kDebugMode) {
          print('Warning: Payment methods data is missing or not a list in API response.');
        }
        return []; // Default to empty list if not found or malformed
      }
    } catch (e) {
      // Log or handle specific errors if needed, then rethrow as ApiException
      // for consistent error handling in the repository layer.
      if (kDebugMode) {
        print('Error fetching payment methods: $e');
      }
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to fetch payment methods: ${e.toString()}',
        code: 'payment_methods_fetch_failed',
      );
    }
  }
  
  /// Format phone number to match backend validation pattern
  /// Backend expects: +[1-9]\d{6,14} (e.g., +252683227649)
  /// Common input: +2520683227649 (with extra 0)
  String _formatPhoneForBackend(String phoneNumber) {
    String cleaned = phoneNumber.trim();
    
    // If it starts with +252 and has extra 0, remove it
    if (cleaned.startsWith('+2520')) {
      cleaned = '+252${cleaned.substring(5)}';
    }
    // If it starts with 252 and has extra 0, add + and remove 0
    else if (cleaned.startsWith('2520') && cleaned.length > 10) {
      cleaned = '+252${cleaned.substring(4)}';
    }
    // If it starts with 0 and looks like local number, add +252
    else if (cleaned.startsWith('0') && cleaned.length >= 9) {
      cleaned = '+252${cleaned.substring(1)}';
    }
    // If it's just digits and looks like local number, add +252
    else if (RegExp(r'^\d{8,9}$').hasMatch(cleaned)) {
      cleaned = '+252$cleaned';
    }
    
    return cleaned;
  }

  // Validate API response
  void _validateResponse(dynamic response) {
    // Check for null response
    if (response == null) {
      throw ApiException(
        message: 'No response received from server',
        code: 'no_response',
      );
    }
    
    // Check for error response
    if (response['success'] == false) {
      final message = response['message'] ?? 'Unknown error';
      final code = response['errorCode'] ?? 'unknown_error';
      throw ApiException(message: message, code: code);
    }
    
    // Check for missing data when expected
    if (!response.containsKey('data') && response['success'] == true) {
      debugPrint('Warning: Success response without data property');
      // Not throwing exception here as some endpoints return success without data
    }
  }
}

