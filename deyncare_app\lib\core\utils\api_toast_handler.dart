import 'package:deyncare_app/core/exceptions/api_exception.dart';
import 'package:deyncare_app/core/utils/toast_util.dart';
import 'package:flutter/foundation.dart';

/// Utility class for handling API responses and showing appropriate toast messages
class ApiToastHandler {
  /// Private constructor to prevent instantiation
  const ApiToastHandler._();

  /// Handle API success with toast notification
  static void handleSuccess({required String message, bool showToast = true}) {
    if (showToast) {
      ToastUtil.showSuccess(message);
    }
    
    // Log success message in debug mode
    if (kDebugMode) {
      print('✅ API Success: $message');
    }
  }

  /// Handle API error with toast notification
  static void handleError(dynamic error, {String? fallbackMessage, bool showToast = true}) {
    String errorMessage = fallbackMessage ?? 'An unexpected error occurred';
    
    // Extract user-friendly message from ApiException
    if (error is ApiException) {
      errorMessage = error.message;
      
      // Special handling for specific error types
      if (error.code == 'rate_limit_exceeded' || error.statusCode == 429) {
        errorMessage = 'You\'re making requests too quickly. Please wait a moment and try again.';
      }
      // Don't override login error messages - they should be preserved as-is
      else if (error.code == 'login_failed' || error.code.contains('login') == true) {
        // Keep the original message from the backend
        errorMessage = error.message;
      }
    } 
    // Handle DioException and other errors 
    else if (error != null) {
      final errorString = error.toString();
      
      // Common error cases with friendly messages
      if (errorString.contains('No route to host') || 
          errorString.contains('Failed host lookup') ||
          errorString.contains('Connection refused')) {
        errorMessage = 'Cannot connect to server. Please check your network connection.';
      } else if (errorString.contains('timed out')) {
        errorMessage = 'Connection timed out. Please try again later.';
      } else if (errorString.contains('Invalid file type')) {
        errorMessage = 'Invalid file type. Please use JPG, PNG, or PDF files.';
      } else if (errorString.contains('Email already exists')) {
        errorMessage = 'This email is already registered.';
      } else if (errorString.contains('Token expired')) {
        errorMessage = 'Your session has expired. Please log in again.';
      } else if (errorString.contains('Invalid email or password')) {
        // Preserve login error messages
        errorMessage = 'Invalid email or password';
      } else if (errorString.contains('Unauthorized') || errorString.contains('401')) {
        errorMessage = 'You are not authorized to perform this action.';
      } else if (errorString.contains('Too many requests') || errorString.contains('429')) {
        errorMessage = 'You\'re making requests too quickly. Please wait a moment and try again.';
      } else if (errorString.contains('Server error') || errorString.contains('500')) {
        errorMessage = 'Server error. Our team has been notified of the issue.';
      } else {
        // Clean up the error message
        errorMessage = errorString.replaceAll('Exception: ', '');
        if (errorMessage.length > 100) {
          errorMessage = '${errorMessage.substring(0, 97)}...';
        }
      }
    }
    
    if (showToast) {
      ToastUtil.showError(errorMessage);
    }
    
    // Log error in debug mode
    if (kDebugMode) {
      print('❌ API Error: $errorMessage');
      if (error != null) {
        print('Original error: $error');
      }
    }
  }

  /// Handle API warnings with toast notification
  static void handleWarning(String message, {bool showToast = true}) {
    if (showToast) {
      ToastUtil.showWarning(message);
    }
    
    // Log warning in debug mode
    if (kDebugMode) {
      print('⚠️ API Warning: $message');
    }
  }

  /// Handle informational messages with toast notification
  static void handleInfo(String message, {bool showToast = true}) {
    if (showToast) {
      ToastUtil.showInfo(message);
    }
    
    // Log info in debug mode
    if (kDebugMode) {
      print('ℹ️ API Info: $message');
    }
  }
  
  /// Cancel all active toast notifications
  static void cancelAll() {
    ToastUtil.cancelAll();
    
    // Log in debug mode
    if (kDebugMode) {
      print('🚫 Cancelled all toast notifications');
    }
  }
}
