"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { useResponsive } from "@/hooks/use-responsive";
import { useAuth } from "@/contexts/auth-context";
import { SettingsProvider } from "@/contexts/settings";
import { Sidebar } from "./sidebar";
import { Header } from "./header";
import { Footer } from "./footer";
import { toast } from "sonner";

export function DashboardLayout({ children }) {
  const router = useRouter();
  const { isAuthenticated, isSuperAdmin, user, loading } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { isMobile, isTablet } = useResponsive();
  
  // Close sidebar on mobile by default
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    } else {
      setSidebarOpen(true);
    }
  }, [isMobile]);
  
  // Handle authentication
  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        router.push("/login");
      } else if (!isSuperAdmin) {
        toast.error("Access restricted. This web application is for superadmins only.");
        router.push("/unauthorized");
      }
    }
  }, [isAuthenticated, isSuperAdmin, loading, router]);
  
  // Toggle mobile sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  
  // If still loading auth state or not authenticated, show loading state
  if (loading || !isAuthenticated || !isSuperAdmin) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }
  
  return (
    <SettingsProvider>
      {/* Fixed height container */}
      <div className="h-screen bg-background">
        {/* Fixed Header - Always at top of viewport */}
        <Header onToggleSidebar={toggleSidebar} />

        {/* Main layout container */}
        <div className="flex h-[calc(100vh-4rem)]"> {/* Subtract header height */}
          {/* Sidebar - Always fixed position */}
          <div
            className={cn(
              "fixed inset-y-16 left-0 z-50 transform transition-transform duration-300",
              sidebarOpen ? "translate-x-0" : "-translate-x-full",
              "w-64" // Fixed width for consistency
            )}
          >
            <Sidebar />
          </div>

          {/* Mobile sidebar overlay */}
          {isMobile && sidebarOpen && (
            <div
              className="fixed inset-0 z-40 bg-black/50 transition-opacity lg:hidden"
              onClick={toggleSidebar}
              style={{ top: '4rem' }} // Start below header
            />
          )}

          {/* Main content area - Single scroll container */}
          <div
            className={cn(
              "flex flex-1 flex-col min-h-0 transition-all duration-300",
              sidebarOpen && !isMobile ? "ml-64" : "ml-0" // Adjust margin based on sidebar
            )}
          >
            {/* Main scrollable content */}
            <main className="flex-1 single-scroll-container">
              <div className="p-4 lg:p-6 min-w-0 prevent-double-scroll">
                <div className="mx-auto w-full max-w-7xl min-w-0">
                  {children}
                </div>
              </div>
            </main>

            {/* Footer - flows with content */}
            <Footer />
          </div>
        </div>
      </div>
    </SettingsProvider>
  );
}
