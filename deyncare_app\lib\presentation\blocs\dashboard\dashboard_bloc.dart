import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_event.dart';
import 'package:deyncare_app/presentation/blocs/dashboard/dashboard_state.dart';
import 'package:deyncare_app/core/utils/api_toast_handler.dart';
import 'package:logger/logger.dart';

// Import existing use cases for real data integration
import 'package:deyncare_app/domain/usecases/debt/get_debt_analytics_use_case.dart';
import 'package:deyncare_app/domain/usecases/debt/get_debts_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/get_customers_use_case.dart';
import 'package:deyncare_app/domain/models/debt.dart';
import 'package:deyncare_app/domain/repositories/debt_repository.dart';

/// BLoC that manages the dashboard state throughout the app
/// DashboardBloc manages dashboard data and interactions using BLoC pattern
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final Logger _logger = Logger();

  // Real use cases for data integration
  final GetDebtAnalyticsUseCase _getDebtAnalyticsUseCase;
  final GetDebtsUseCase _getDebtsUseCase;
  final GetCustomersUseCase _getCustomersUseCase;

  // Timer for periodic refresh
  Timer? _refreshTimer;

  /// Create a new DashboardBloc with use case dependencies
  DashboardBloc({
    required GetDebtAnalyticsUseCase getDebtAnalyticsUseCase,
    required GetDebtsUseCase getDebtsUseCase,
    required GetCustomersUseCase getCustomersUseCase,
  }) : _getDebtAnalyticsUseCase = getDebtAnalyticsUseCase,
       _getDebtsUseCase = getDebtsUseCase,
       _getCustomersUseCase = getCustomersUseCase,
       super(DashboardInitial()) {
    _logger.d('DashboardBloc: Initialized with state: ${state.runtimeType}');
    
    // Register event handlers
    on<DashboardInitialized>(_onDashboardInitialized);
    on<DashboardRefreshed>(_onDashboardRefreshed);
    on<KPIDataRequested>(_onKPIDataRequested);
    on<RecentActivityRequested>(_onRecentActivityRequested);
    on<ChartDataRequested>(_onChartDataRequested);
    on<KPICardTapped>(_onKPICardTapped);
    on<QuickActionTapped>(_onQuickActionTapped);
    on<ActivityItemTapped>(_onActivityItemTapped);
    on<NavigationTabChanged>(_onNavigationTabChanged);
    on<NotificationsRequested>(_onNotificationsRequested);
    on<NotificationTapped>(_onNotificationTapped);
    on<NotificationMarkedAsRead>(_onNotificationMarkedAsRead);
    on<PullToRefreshTriggered>(_onPullToRefreshTriggered);
    on<DashboardSearchRequested>(_onDashboardSearchRequested);
    on<DashboardFiltersApplied>(_onDashboardFiltersApplied);
    on<DashboardFiltersCleared>(_onDashboardFiltersCleared);
    on<DashboardRetryRequested>(_onDashboardRetryRequested);
    on<DashboardConnectivityChanged>(_onDashboardConnectivityChanged);
  }

  /// Handle dashboard initialization event with optimized loading
  Future<void> _onDashboardInitialized(
    DashboardInitialized event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - DashboardInitialized');
    emit(DashboardLoading(loadingMessage: 'Loading dashboard...'));
    
    try {
      // Load all dashboard data concurrently for faster loading
      final results = await Future.wait([
        _loadKPIData('today'),
        _loadRecentActivity(10),
        _loadChartData('payments', 'month'),
        _loadNotifications(),
      ]);

      final kpiData = results[0] as KPIData;
      final recentActivity = results[1] as List<ActivityItem>;
      final chartData = results[2] as List<ChartData>;
      final notifications = results[3] as List<NotificationItem>;

      emit(DashboardLoaded(
        kpiData: kpiData,
        recentActivity: recentActivity,
        chartData: chartData,
        notifications: notifications,
        lastUpdated: DateTime.now(),
      ));
      
      _logger.d('DashboardBloc: Emitting DashboardLoaded');
      
      // Note: Removed automatic refresh to improve UX - user can manually refresh via pull-to-refresh
    } catch (e) {
      _logger.e('DashboardBloc: Error during initialization: $e');
      emit(DashboardError(
        message: 'Failed to load dashboard data',
        canRetry: true,
      ));
    }
  }

  @override
  Future<void> close() {
    _refreshTimer?.cancel();
    return super.close();
  }

  /// Handle dashboard refresh event
  Future<void> _onDashboardRefreshed(
    DashboardRefreshed event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - DashboardRefreshed');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(DashboardRefreshing(previousState: currentState));
      
      try {
        // Refresh all data
        final results = await Future.wait([
          _loadKPIData('today'),
          _loadRecentActivity(10),
          _loadChartData('payments', 'month'),
          _loadNotifications(),
        ]);

        final kpiData = results[0] as KPIData;
        final recentActivity = results[1] as List<ActivityItem>;
        final chartData = results[2] as List<ChartData>;
        final notifications = results[3] as List<NotificationItem>;

        emit(currentState.copyWith(
          kpiData: kpiData,
          recentActivity: recentActivity,
          chartData: chartData,
          notifications: notifications,
          lastUpdated: DateTime.now(),
          isRefreshing: false,
        ));
        
        ApiToastHandler.handleSuccess(message: 'Dashboard refreshed');
        _logger.d('DashboardBloc: Dashboard refreshed successfully');
      } catch (e) {
        _logger.e('DashboardBloc: Error during refresh: $e');
        emit(currentState.copyWith(isRefreshing: false));
        ApiToastHandler.handleError('Failed to refresh dashboard');
      }
    }
  }

  /// Handle KPI data request event
  Future<void> _onKPIDataRequested(
    KPIDataRequested event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - KPIDataRequested for period: ${event.period}');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      
      try {
        final kpiData = await _loadKPIData(event.period);
        emit(currentState.copyWith(
          kpiData: kpiData,
          lastUpdated: DateTime.now(),
        ));
        _logger.d('DashboardBloc: KPI data updated for period: ${event.period}');
      } catch (e) {
        _logger.e('DashboardBloc: Error loading KPI data: $e');
        ApiToastHandler.handleError('Failed to load KPI data');
      }
    }
  }

  /// Handle recent activity request event
  Future<void> _onRecentActivityRequested(
    RecentActivityRequested event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - RecentActivityRequested with limit: ${event.limit}');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      
      try {
        final recentActivity = await _loadRecentActivity(event.limit);
        emit(currentState.copyWith(
          recentActivity: recentActivity,
          lastUpdated: DateTime.now(),
        ));
        _logger.d('DashboardBloc: Recent activity updated');
      } catch (e) {
        _logger.e('DashboardBloc: Error loading recent activity: $e');
        ApiToastHandler.handleError('Failed to load recent activity');
      }
    }
  }

  /// Handle chart data request event
  Future<void> _onChartDataRequested(
    ChartDataRequested event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - ChartDataRequested for ${event.chartType}');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      
      try {
        final chartData = await _loadChartData(event.chartType, event.period);
        
        // Update or add the chart data
        final updatedChartData = List<ChartData>.from(currentState.chartData);
        final existingIndex = updatedChartData.indexWhere((chart) => chart.type == event.chartType);
        
        if (existingIndex != -1) {
          updatedChartData[existingIndex] = chartData.first;
        } else {
          updatedChartData.addAll(chartData);
        }
        
        emit(currentState.copyWith(
          chartData: updatedChartData,
          lastUpdated: DateTime.now(),
        ));
        _logger.d('DashboardBloc: Chart data updated for ${event.chartType}');
      } catch (e) {
        _logger.e('DashboardBloc: Error loading chart data: $e');
        ApiToastHandler.handleError('Failed to load chart data');
      }
    }
  }

  /// Handle KPI card tap event
  void _onKPICardTapped(
    KPICardTapped event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - KPICardTapped for ${event.kpiType}');
    
    // Navigate to detailed view based on KPI type
    String destination;
    Map<String, dynamic>? parameters;
    
    switch (event.kpiType) {
      case 'payments':
        destination = '/payments-details';
        parameters = {'period': 'today'};
        break;
      case 'debts':
        destination = '/debts-overview';
        break;
      case 'customers':
        destination = '/customers-list';
        break;
      case 'risk':
        destination = '/risk-analysis';
        break;
      default:
        destination = '/dashboard';
    }
    
    emit(DashboardNavigationTriggered(
      destination: destination,
      parameters: parameters,
    ));
  }

  /// Handle quick action tap event
  void _onQuickActionTapped(
    QuickActionTapped event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - QuickActionTapped for ${event.actionType}');
    
    // Navigate to appropriate screen based on action type
    String destination;
    Map<String, dynamic>? parameters;
    
    switch (event.actionType) {
      case 'add_customer':
        destination = '/add-customer';
        break;
      case 'record_payment':
        destination = '/record-payment';
        break;
      case 'reports':
        destination = '/reports';
        break;
      case 'settings':
        destination = '/settings';
        break;
      case 'inventory':
        destination = '/inventory';
        break;
      case 'send_reminder':
        destination = '/send-reminder';
        break;
      case 'alerts':
        destination = '/alerts';
        break;
      default:
        destination = '/dashboard';
    }
    
    emit(DashboardNavigationTriggered(
      destination: destination,
      parameters: parameters,
    ));
  }

  /// Handle activity item tap event
  void _onActivityItemTapped(
    ActivityItemTapped event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - ActivityItemTapped for ${event.activityType}');
    
    // Navigate to detailed view based on activity type
    String destination;
    Map<String, dynamic> parameters = {'id': event.activityId};
    
    switch (event.activityType) {
      case 'payment':
        destination = '/payment-details';
        break;
      case 'customer':
        destination = '/customer-details';
        break;
      case 'debt':
        destination = '/debt-details';
        break;
      default:
        destination = '/dashboard';
    }
    
    emit(DashboardNavigationTriggered(
      destination: destination,
      parameters: parameters,
    ));
  }

  /// Handle navigation tab change event
  void _onNavigationTabChanged(
    NavigationTabChanged event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - NavigationTabChanged to index: ${event.tabIndex}');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(currentState.copyWith(currentTabIndex: event.tabIndex));
    }
  }

  /// Handle notifications request event
  Future<void> _onNotificationsRequested(
    NotificationsRequested event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - NotificationsRequested');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      
      try {
        final notifications = await _loadNotifications();
        emit(currentState.copyWith(
          notifications: notifications,
          lastUpdated: DateTime.now(),
        ));
        _logger.d('DashboardBloc: Notifications updated');
      } catch (e) {
        _logger.e('DashboardBloc: Error loading notifications: $e');
        ApiToastHandler.handleError('Failed to load notifications');
      }
    }
  }

  /// Handle notification tap event
  void _onNotificationTapped(
    NotificationTapped event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - NotificationTapped for ${event.notificationId}');
    
    // Mark notification as read and navigate if needed
    add(NotificationMarkedAsRead(notificationId: event.notificationId));
    
    // Handle navigation based on notification action data
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      final notification = currentState.notifications.firstWhere(
        (n) => n.id == event.notificationId,
        orElse: () => NotificationItem(
          id: '',
          title: '',
          message: '',
          type: 'info',
          timestamp: DateTime.now(),
        ),
      );
      
      if (notification.actionData != null) {
        final destination = notification.actionData!['destination'] as String? ?? '/dashboard';
        final parameters = notification.actionData!['parameters'] as Map<String, dynamic>?;
        
        emit(DashboardNavigationTriggered(
          destination: destination,
          parameters: parameters,
        ));
      }
    }
  }

  /// Handle notification mark as read event
  void _onNotificationMarkedAsRead(
    NotificationMarkedAsRead event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - NotificationMarkedAsRead for ${event.notificationId}');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      final updatedNotifications = currentState.notifications.map((notification) {
        if (notification.id == event.notificationId) {
          return NotificationItem(
            id: notification.id,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            timestamp: notification.timestamp,
            isRead: true,
            actionData: notification.actionData,
          );
        }
        return notification;
      }).toList();
      
      emit(currentState.copyWith(notifications: updatedNotifications));
    }
  }

  /// Handle pull to refresh event
  Future<void> _onPullToRefreshTriggered(
    PullToRefreshTriggered event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - PullToRefreshTriggered');
    add(DashboardRefreshed());
  }

  /// Handle dashboard search event
  Future<void> _onDashboardSearchRequested(
    DashboardSearchRequested event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.d('DashboardBloc: Event received - DashboardSearchRequested for "${event.query}"');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      
      try {
        final results = await _performSearch(event.query, event.searchType);
        emit(DashboardSearchResults(
          query: event.query,
          searchType: event.searchType,
          results: results,
          baseState: currentState,
        ));
        _logger.d('DashboardBloc: Search completed with ${results.length} results');
      } catch (e) {
        _logger.e('DashboardBloc: Error during search: $e');
        ApiToastHandler.handleError('Search failed');
      }
    }
  }

  /// Handle filters applied event
  void _onDashboardFiltersApplied(
    DashboardFiltersApplied event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - DashboardFiltersApplied');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(currentState.copyWith(appliedFilters: event.filters));
      
      // Refresh data with new filters
      add(DashboardRefreshed());
    }
  }

  /// Handle filters cleared event
  void _onDashboardFiltersCleared(
    DashboardFiltersCleared event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - DashboardFiltersCleared');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(currentState.copyWith(appliedFilters: null));
      
      // Refresh data without filters
      add(DashboardRefreshed());
    }
  }

  /// Handle retry request event
  void _onDashboardRetryRequested(
    DashboardRetryRequested event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - DashboardRetryRequested');
    add(DashboardInitialized());
  }

  /// Handle connectivity change event
  void _onDashboardConnectivityChanged(
    DashboardConnectivityChanged event,
    Emitter<DashboardState> emit,
  ) {
    _logger.d('DashboardBloc: Event received - DashboardConnectivityChanged: ${event.isConnected}');
    
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(currentState.copyWith(isConnected: event.isConnected));
      
      if (event.isConnected) {
        // Refresh data when coming back online
        add(DashboardRefreshed());
        ApiToastHandler.handleSuccess(message: 'Connection restored');
      } else {
        ApiToastHandler.handleWarning('You are offline');
      }
    } else if (!event.isConnected) {
      emit(DashboardOffline(cachedState: state is DashboardLoaded ? state as DashboardLoaded : null));
    }
  }

  // Private helper methods for data loading (mock implementations)
  
  /// Load KPI data for the specified period using real data
  Future<KPIData> _loadKPIData(String period) async {
    try {
      // Get real analytics data from debt use case
      final analyticsResult = await _getDebtAnalyticsUseCase.execute();
      
      return analyticsResult.fold(
        (failure) {
          // Return mock data if failed to load real data
          _logger.w('Failed to load debt analytics: $failure');
          return KPIData(
            todayPayments: 0.0,
            outstandingDebts: 0.0,
            newCustomers: 0,
            riskLevel: 'Unknown',
            paymentsGrowth: 0.0,
            overdueDebts: 0,
            riskTrend: 'Unknown',
          );
        },
                 (analytics) {
           // Convert analytics to KPI data using proper property access
           return KPIData(
             todayPayments: analytics.stats.totalPaid,
             outstandingDebts: analytics.stats.totalOutstanding,
             newCustomers: analytics.stats.totalDebts, // Use total debts as proxy for new customers
             riskLevel: _calculateRiskLevel(analytics.riskDistribution),
             paymentsGrowth: analytics.stats.collectionRate,
             overdueDebts: analytics.stats.overdueDebts,
             riskTrend: _calculateRiskTrend(analytics.monthlyTrends),
           );
         },
      );
    } catch (e) {
      _logger.e('Error loading KPI data: $e');
      // Return empty data on error
      return KPIData(
        todayPayments: 0.0,
        outstandingDebts: 0.0,
        newCustomers: 0,
        riskLevel: 'Unknown',
        paymentsGrowth: 0.0,
        overdueDebts: 0,
        riskTrend: 'Unknown',
      );
    }
  }

  /// Load recent activity data using real debt data
  Future<List<ActivityItem>> _loadRecentActivity(int limit) async {
    try {
      // Get recent debts to create activity items
      final debtsResult = await _getDebtsUseCase.execute(
        page: 1,
        limit: limit,
        sortBy: 'createdAt',
        ascending: false,
      );
      
      return debtsResult.fold(
        (failure) {
          _logger.w('Failed to load recent debts: $failure');
          return <ActivityItem>[]; // Return empty list on failure
        },
        (debtListResult) {
                     // Convert recent debts to activity items
           return debtListResult.debts.map((debt) {
             return ActivityItem(
               id: debt.debtId,
               type: debt.status == DebtStatus.completed ? 'payment' : 'debt',
               title: debt.status == DebtStatus.completed 
                   ? 'Payment received - ${debt.customerName}'
                   : 'Debt created - ${debt.customerName}',
               subtitle: debt.description ?? 'Debt management',
               amount: '\$${debt.amount.toStringAsFixed(2)}',
               timestamp: debt.createdAt,
               icon: debt.status == DebtStatus.completed ? 'payment' : 'warning',
               status: debt.status == DebtStatus.overdue ? 'pending' : 'completed',
             );
           }).toList();
        },
      );
    } catch (e) {
      _logger.e('Error loading recent activity: $e');
      return <ActivityItem>[];
    }
  }

  /// Load chart data for the specified type and period using real analytics
  Future<List<ChartData>> _loadChartData(String chartType, String period) async {
    try {
      // Use debt analytics for chart data
      final analyticsResult = await _getDebtAnalyticsUseCase.execute();
      
      return analyticsResult.fold(
        (failure) {
          _logger.w('Failed to load chart data: $failure');
          return <ChartData>[]; // Return empty list on failure
        },
        (analytics) {
          // Convert monthly trends to chart data
          final chartData = analytics.monthlyTrends.map((trend) {
            return {
              'x': trend.month.month.toString(),
              'y': trend.totalAmount.toInt(),
            };
          }).toList();
          
          return [
            ChartData(
              type: chartType,
              data: chartData.isNotEmpty ? chartData : [
                {'x': 'Jan', 'y': 0},
                {'x': 'Feb', 'y': 0},
                {'x': 'Mar', 'y': 0},
              ],
              period: period,
              lastUpdated: DateTime.now(),
            ),
          ];
        },
      );
    } catch (e) {
      _logger.e('Error loading chart data: $e');
      return <ChartData>[];
    }
  }

  /// Load notifications data from overdue debts
  Future<List<NotificationItem>> _loadNotifications() async {
    try {
      // Get overdue debts to create notifications
      final debtsResult = await _getDebtsUseCase.execute(
        page: 1,
        limit: 10,
        status: DebtStatus.overdue,
        sortBy: 'dueDate',
        ascending: false,
      );
      
      return debtsResult.fold(
        (failure) {
          _logger.w('Failed to load notifications: $failure');
          return <NotificationItem>[];
        },
        (debtListResult) {
          // Convert overdue debts to notification items
          return debtListResult.debts.map((debt) {
            return NotificationItem(
              id: debt.debtId,
              title: 'Payment Overdue',
              message: '${debt.customerName} has an overdue payment of \$${debt.remainingAmount.toStringAsFixed(2)}',
              type: 'warning',
              timestamp: debt.dueDate,
              actionData: {
                'destination': '/debt-details',
                'parameters': {'debtId': debt.debtId},
              },
            );
          }).toList();
        },
      );
    } catch (e) {
      _logger.e('Error loading notifications: $e');
      return <NotificationItem>[];
    }
  }

  /// Perform search operation
  Future<List<Map<String, dynamic>>> _performSearch(String query, String searchType) async {
    // TODO: Replace with actual search use case
    
    // Mock implementation
    await Future.delayed(Duration(milliseconds: 300));
    return [
      {
        'id': '1',
        'type': searchType,
        'title': 'Ahmed Hassan',
        'subtitle': 'Customer since 2023',
        'data': {'phone': '+252123456789', 'debt': 200.0},
      },
      {
        'id': '2',
        'type': searchType,
        'title': 'Fatima Ali',
        'subtitle': 'Customer since 2024',
        'data': {'phone': '+252987654321', 'debt': 0.0},
      },
    ];
  }

  /// Calculate overall risk level from risk distribution
  String _calculateRiskLevel(List<RiskDistribution> riskDistribution) {
    if (riskDistribution.isEmpty) return 'Unknown';
    
    // Find the risk level with highest percentage
    var maxRisk = riskDistribution.reduce((a, b) => a.percentage > b.percentage ? a : b);
    return maxRisk.riskLevel.toString().split('.').last;
  }

  /// Calculate risk trend from monthly trends
  String _calculateRiskTrend(List<MonthlyTrend> monthlyTrends) {
    if (monthlyTrends.length < 2) return 'Unknown';
    
    // Compare collection rates between recent months
    var sortedTrends = monthlyTrends..sort((a, b) => a.month.compareTo(b.month));
    var recent = sortedTrends.last.collectionRate;
    var previous = sortedTrends[sortedTrends.length - 2].collectionRate;
    
    if (recent > previous) {
      return 'Improving';
    } else if (recent < previous) {
      return 'Declining';
    } else {
      return 'Stable';
    }
  }
} 