@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * DeynCare color palette
 * Primary Blue: hsl(215, 100%, 50%)
 * Light Blue: hsl(215, 100%, 65%)
 * Dark Blue: hsl(215, 100%, 35%)
 */

@layer base {
  :root {
    --radius: 0.625rem;
    
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    /* Brand colors - DeynCare Blue */
    --primary: 215 100% 50%;
    --primary-foreground: 0 0% 100%;
    
    /* Secondary colors */
    --secondary: 215 30% 94%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    
    /* Muted colors */
    --muted: 215 20% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    /* Accent colors */
    --accent: 215 30% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    
    /* Destructive colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Border colors */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 215 100% 50%;
    
    /* Chart colors */
    --chart-1: 215 80% 60%;
    --chart-2: 200 80% 60%;
    --chart-3: 240 80% 60%;
    --chart-4: 280 60% 60%;
    --chart-5: 340 60% 60%;
    
    /* Sidebar colors */
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 215 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 30% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 215 100% 50%;
  }

  .dark {
    /* Base colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    /* Card colors */
    --card: 222.2 47.4% 11.2%;
    --card-foreground: 210 40% 98%;
    
    /* Popover colors */
    --popover: 222.2 47.4% 11.2%;
    --popover-foreground: 210 40% 98%;
    
    /* Brand colors - DeynCare Blue */
    --primary: 215 100% 60%; /* Brighter blue in dark mode */
    --primary-foreground: 0 0% 100%;
    
    /* Secondary colors */
    --secondary: 215 30% 18%;
    --secondary-foreground: 210 40% 98%;
    
    /* Muted colors */
    --muted: 215 30% 17.5%;
    --muted-foreground: 215 20% 65.1%;
    
    /* Accent colors */
    --accent: 215 30% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    /* Destructive colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    
    /* Border colors */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 215 100% 60%;
    
    /* Chart colors */
    --chart-1: 215 80% 60%;
    --chart-2: 200 80% 60%;
    --chart-3: 240 80% 60%;
    --chart-4: 280 60% 60%;
    --chart-5: 340 60% 60%;
    
    /* Sidebar colors */
    --sidebar: 222.2 47.4% 11.2%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 215 100% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 30% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 28% 17%;
    --sidebar-ring: 215 100% 60%;
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Ensure smooth scrolling */
  .scroll-smooth {
    scroll-behavior: smooth;
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Standardized scroll containers - USE THESE TO PREVENT MULTIPLE SCROLL ISSUES */
  .single-scroll-container {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }

  .single-scroll-container::-webkit-scrollbar {
    width: 6px;
  }

  .single-scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .single-scroll-container::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }

  .single-scroll-container::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground));
  }

  /* Prevent nested scroll containers */
  .no-scroll {
    overflow: visible !important;
  }

  .prevent-double-scroll * {
    overflow-y: visible !important;
  }

  .prevent-double-scroll .overflow-y-auto {
    overflow-y: visible !important;
  }

  /* More aggressive prevention of nested scrolls */
  .prevent-double-scroll div[class*="overflow"] {
    overflow-y: visible !important;
  }

  .prevent-double-scroll [style*="overflow"] {
    overflow-y: visible !important;
  }

  /* Specifically target common scroll-creating elements */
  .prevent-double-scroll .overflow-auto {
    overflow-y: visible !important;
    overflow-x: auto; /* Allow horizontal scroll only */
  }

  .prevent-double-scroll .overflow-hidden {
    overflow: visible !important;
  }

  /* Ensure table containers don't create vertical scroll */
  .prevent-double-scroll .table-container {
    overflow-y: visible !important;
  }

  /* Table containers - prevent double scroll while allowing horizontal scroll only when needed */
  .table-container {
    overflow-x: auto;
    overflow-y: visible;
    /* Only show horizontal scrollbar when content actually overflows */
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }

  .table-container::-webkit-scrollbar {
    height: 6px;
    width: 0; /* No vertical scrollbar */
  }

  .table-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }

  .table-container::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground));
  }

  /* Dialog containers - standardized to prevent nested scrolls */
  .dialog-content {
    max-height: 85vh;
    display: flex;
    flex-direction: column;
  }
  
  .dialog-header {
    flex-shrink: 0;
  }
  
  .dialog-body {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }
  
  .dialog-footer {
    flex-shrink: 0;
    border-top: 1px solid hsl(var(--border));
    margin-top: auto;
  }

  /* Responsive container improvements */
  .responsive-container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  @media (min-width: 640px) {
    .responsive-container {
      max-width: 640px;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
  
  @media (min-width: 768px) {
    .responsive-container {
      max-width: 768px;
    }
  }
  
  @media (min-width: 1024px) {
    .responsive-container {
      max-width: 1024px;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  
  @media (min-width: 1280px) {
    .responsive-container {
      max-width: 1280px;
    }
  }

  /* Mobile optimization */
  @media (max-width: 768px) {
    .mobile-optimized {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }
    
    .mobile-full-width {
      width: 100vw;
      margin-left: calc(-50vw + 50%);
    }
  }

  /* Prevent layout shift and improve performance */
  .layout-stable {
    contain: layout style paint;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* Fixed layout improvements */
  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    height: 4rem; /* 64px */
  }

  .fixed-sidebar {
    position: fixed;
    top: 4rem; /* Below header */
    bottom: 0;
    left: 0;
    width: 16rem; /* 256px */
    z-index: 40;
  }

  .main-content-with-sidebar {
    margin-left: 16rem; /* 256px - sidebar width */
    margin-top: 4rem; /* 64px - header height */
    min-height: calc(100vh - 4rem);
  }

  .main-content-without-sidebar {
    margin-left: 0;
    margin-top: 4rem; /* 64px - header height */
    min-height: calc(100vh - 4rem);
  }

  /* Responsive adjustments */
  @media (max-width: 1023px) {
    .main-content-with-sidebar {
      margin-left: 0;
    }
  }

  /* 404 Page Animations */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
    opacity: 0;
  }

  /* Enhanced gradient text */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
