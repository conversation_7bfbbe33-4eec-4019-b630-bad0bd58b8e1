import 'package:deyncare_app/data/models/employee_model.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';
import 'package:deyncare_app/core/exceptions/api_exception.dart';

/// Service for user/employee management API operations
class UserManagementService {
  final DioClient _dioClient;

  UserManagementService(this._dioClient);

  /// Get all employees for the current shop
  Future<List<EmployeeModel>> getEmployees({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (search != null && search.isNotEmpty) 'search': search,
      };

      final response = await _dioClient.get(
        '/users/employees', // ✅ Correct path
        queryParameters: queryParams,
      );

      // DioClient returns processed response data directly, not wrapped in .data
      if (response['success'] == true) {
        final List<dynamic> employeesData = response['data']['employees'] ?? [];

        return employeesData
            .map((employee) => EmployeeModel.fromJson(employee))
            .toList();
      } else {
        throw ApiException(
            message: response['message'] ?? 'Failed to fetch employees');
      }
    } catch (e) {
      throw ApiException(message: 'Failed to fetch employees: ${e.toString()}');
    }
  }

  /// Get specific employee by ID
  Future<EmployeeModel> getEmployeeById(String employeeId) async {
    try {
      final response = await _dioClient.get('/users/employees/$employeeId');

      return EmployeeModel.fromJson(response['data']);
    } catch (e) {
      throw ApiException(message: 'Failed to fetch employee: ${e.toString()}');
    }
  }

  /// Create new employee
  Future<void> createEmployee(CreateEmployeeRequestModel request) async {
    try {
      await _dioClient.post(
        '/auth/create-employee',
        data: request.toJson(),
      );
    } catch (e) {
      throw ApiException(message: 'Failed to create employee: ${e.toString()}');
    }
  }

  /// Update employee permissions
  Future<EmployeeModel> updateEmployeePermissions(
    String employeeId,
    UpdateEmployeeRequestModel request,
  ) async {
    try {
      final response = await _dioClient.put(
        '/users/employees/$employeeId/permissions',
        data: request.toJson(),
      );

      return EmployeeModel.fromJson(response['data']);
    } catch (e) {
      throw ApiException(message: 'Failed to update employee: ${e.toString()}');
    }
  }

  /// Delete employee
  Future<void> deleteEmployee(String employeeId) async {
    try {
      await _dioClient.delete('/users/employees/$employeeId');
    } catch (e) {
      throw ApiException(message: 'Failed to delete employee: ${e.toString()}');
    }
  }

  /// Alias for getEmployees to maintain backward compatibility
  Future<List<EmployeeModel>> getAllShopUsers({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    return getEmployees(page: page, limit: limit, search: search);
  }

  /// Validate and extract employee data from API response
  List<dynamic> _extractEmployeeData(dynamic response) {
    if (response == null) {
      throw ApiException(
        message: 'No response received from server',
        code: 'no_response',
      );
    }

    if (response is! Map<String, dynamic>) {
      throw ApiException(
        message: 'Invalid response format',
        code: 'invalid_format',
      );
    }

    final data = response['data'];

    // Handle different possible response structures
    if (data is List) {
      return data;
    } else if (data is Map) {
      // Try different possible keys
      for (final key in ['employees', 'users', 'items', 'results']) {
        if (data.containsKey(key) && data[key] is List) {
          return data[key];
        }
      }
    }

    // If no valid structure found, return empty list
    return [];
  }
}
