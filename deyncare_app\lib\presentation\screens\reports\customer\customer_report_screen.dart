import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/core/services/pdf_service.dart';
import 'package:deyncare_app/presentation/blocs/reports/customer_report_bloc.dart';
import 'package:deyncare_app/presentation/widgets/common_app_bar.dart';
import 'package:deyncare_app/presentation/widgets/common_card.dart';
import 'package:deyncare_app/presentation/widgets/common_button.dart';

class CustomerReportScreen extends StatefulWidget {
  const CustomerReportScreen({super.key});

  @override
  State<CustomerReportScreen> createState() => _CustomerReportScreenState();
}

class _CustomerReportScreenState extends State<CustomerReportScreen> {
  String _selectedPeriod = 'monthly';
  DateTime _selectedDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: const CommonAppBar(
        title: 'Customer Report',
      ),
      body: BlocListener<CustomerReportBloc, CustomerReportState>(
        listener: (context, state) {
          if (state is CustomerReportGenerated) {
            _showSuccessDialog(context, state.pdfPath, state.reportPeriod);
          } else if (state is CustomerReportError) {
            _showErrorDialog(context, state.message);
          }
        },
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  const SizedBox(height: 8),
                  
                  // Header Card
                  _buildHeaderCard(context),
                  
                  const SizedBox(height: 24),
                  
                  // Date Selection Card
                  _buildDateSelectionCard(context),
                  
                  const SizedBox(height: 24),
                  
                  // Preview Card
                  _buildPreviewCard(context),
                  
                  // Last Generated Report Card (if available)
                  BlocBuilder<CustomerReportBloc, CustomerReportState>(
                    builder: (context, state) {
                      if (state is CustomerReportGenerated) {
                        return Column(
                          children: [
                            const SizedBox(height: 24),
                            _buildLastReportCard(context, state.pdfPath, state.reportPeriod),
                          ],
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                  
                  const SizedBox(height: 100), // Space for bottom button
                ],
              ),
            ),
            
            // Action Buttons (Fixed at bottom)
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context) {
    return CommonCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppThemes.successColor.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.people_outline,
                  color: AppThemes.successColor,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Customer Report',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Generate detailed customer data reports with filtering options',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelectionCard(BuildContext context) {
    return CommonCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Report Period',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
            ),
          ),
          const SizedBox(height: 16),
          
          // Period Selection
          _buildPeriodSelector(context),
          
          const SizedBox(height: 16),
          
          // Date Picker
          _buildDatePicker(context),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector(BuildContext context) {
    final periods = [
      {'value': 'daily', 'label': 'Daily', 'icon': Icons.today},
      {'value': 'monthly', 'label': 'Monthly', 'icon': Icons.calendar_month},
      {'value': 'yearly', 'label': 'Yearly', 'icon': Icons.calendar_today},
    ];

    return Row(
      children: periods.map((period) {
        final isSelected = _selectedPeriod == period['value'];
        return Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedPeriod = period['value'] as String;
              });
              HapticFeedback.lightImpact();
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppThemes.primaryColor.withValues(alpha: 0.15)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected 
                      ? AppThemes.primaryColor
                      : Theme.of(context).dividerColor,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    period['icon'] as IconData,
                    color: isSelected 
                        ? AppThemes.primaryColor
                        : ThemeUtils.getIconColor(context, type: IconColorType.secondary),
                    size: 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    period['label'] as String,
                    style: TextStyle(
                      color: isSelected 
                          ? AppThemes.primaryColor
                          : ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDatePicker(BuildContext context) {
    return GestureDetector(
      onTap: () => _selectDate(context),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: AppThemes.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Selected ${_selectedPeriod.capitalize()}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _getFormattedDate(),
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: ThemeUtils.getIconColor(context, type: IconColorType.secondary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewCard(BuildContext context) {
    return CommonCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Report Preview',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
            ),
          ),
          const SizedBox(height: 16),
          
          // Preview items
          _buildPreviewItem(context, 'Report Period', _getFormattedDate()),
          _buildPreviewItem(context, 'Report Type', 'Customer Data Report'),
          _buildPreviewItem(context, 'Format', 'PDF Document'),
          _buildPreviewItem(context, 'Estimated Size', '< 1 MB'),
        ],
      ),
    );
  }

  Widget _buildPreviewItem(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: ThemeUtils.getTextColor(context, type: TextColorType.primary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLastReportCard(BuildContext context, String pdfPath, String reportPeriod) {
    return CommonCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: AppThemes.successColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Last Generated Report',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppThemes.successColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Report ready to view or share',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                  ),
                ),
              ),
              Row(
                children: [
                  TextButton.icon(
                    onPressed: () => _openReport(context, CustomerReportGenerated(pdfPath: pdfPath, reportPeriod: reportPeriod)),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('View'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppThemes.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: () => _shareReport(context, CustomerReportGenerated(pdfPath: pdfPath, reportPeriod: reportPeriod)),
                    icon: const Icon(Icons.share, size: 16),
                    label: const Text('Share'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppThemes.infoColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: BlocBuilder<CustomerReportBloc, CustomerReportState>(
          builder: (context, state) {
            final isGenerating = state is CustomerReportGenerating;
            final hasReport = state is CustomerReportGenerated;

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Generate PDF Button
                CommonButton(
                  label: isGenerating ? 'Generating PDF...' : 'Generate PDF Report',
                  onPressed: isGenerating ? null : () => _generatePDFReport(context),
                  isLoading: isGenerating,
                  icon: isGenerating ? null : const Icon(Icons.picture_as_pdf),
                ),
                
                // Quick Actions (if report exists)
                if (hasReport) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _openReport(context, state),
                          icon: const Icon(Icons.visibility, size: 18),
                          label: const Text('View Report'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppThemes.primaryColor,
                            side: BorderSide(color: AppThemes.primaryColor),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _shareReport(context, state),
                          icon: const Icon(Icons.share, size: 18),
                          label: const Text('Share'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppThemes.infoColor,
                            side: BorderSide(color: AppThemes.infoColor),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  String _getFormattedDate() {
    switch (_selectedPeriod) {
      case 'daily':
        return '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}';
      case 'monthly':
        return '${_getMonthName(_selectedDate.month)} ${_selectedDate.year}';
      case 'yearly':
        return '${_selectedDate.year}';
      default:
        return 'All Time';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  Future<void> _selectDate(BuildContext context) async {
    if (_selectedPeriod == 'daily') {
      final date = await showDatePicker(
        context: context,
        initialDate: _selectedDate,
        firstDate: DateTime(2020),
        lastDate: DateTime.now(),
      );
      if (date != null) {
        setState(() {
          _selectedDate = date;
        });
      }
    } else if (_selectedPeriod == 'monthly') {
      // Show month/year picker
      _showMonthYearPicker(context);
    } else if (_selectedPeriod == 'yearly') {
      // Show year picker
      _showYearPicker(context);
    }
  }

  void _showMonthYearPicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Month & Year'),
        content: SizedBox(
          height: 200,
          child: Column(
            children: [
              // Month selection
              DropdownButton<int>(
                value: _selectedDate.month,
                items: List.generate(12, (index) {
                  return DropdownMenuItem(
                    value: index + 1,
                    child: Text(_getMonthName(index + 1)),
                  );
                }),
                onChanged: (month) {
                  if (month != null) {
                    setState(() {
                      _selectedDate = DateTime(
                        _selectedDate.year,
                        month,
                        _selectedDate.day,
                      );
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              // Year selection
              DropdownButton<int>(
                value: _selectedDate.year,
                items: List.generate(11, (index) {
                  final year = DateTime.now().year - index;
                  return DropdownMenuItem(
                    value: year,
                    child: Text(year.toString()),
                  );
                }),
                onChanged: (year) {
                  if (year != null) {
                    setState(() {
                      _selectedDate = DateTime(
                        year,
                        _selectedDate.month,
                        _selectedDate.day,
                      );
                    });
                  }
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {});
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showYearPicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Year'),
        content: SizedBox(
          height: 200,
          child: ListView.builder(
            itemCount: 11,
            itemBuilder: (context, index) {
              final year = DateTime.now().year - index;
              return ListTile(
                title: Text(year.toString()),
                onTap: () {
                  setState(() {
                    _selectedDate = DateTime(year, 1, 1);
                  });
                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _generatePDFReport(BuildContext context) {
    // Prepare parameters based on selected period and date
    String? month, year, startDate, endDate;
    
    if (_selectedPeriod == 'monthly') {
      month = _selectedDate.month.toString();
      year = _selectedDate.year.toString();
    } else if (_selectedPeriod == 'yearly') {
      year = _selectedDate.year.toString();
    } else if (_selectedPeriod == 'daily') {
      startDate = '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}';
      endDate = startDate;
    }

    // Trigger BLoC event
    context.read<CustomerReportBloc>().add(
      GenerateCustomerReportEvent(
        month: month,
        year: year,
        startDate: startDate,
        endDate: endDate,
        dateRange: _selectedPeriod,
      ),
    );
  }

  Future<void> _openReport(BuildContext context, CustomerReportGenerated state) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      await PDFService.openPDF(state.pdfPath);
      // Close loading indicator
      if (mounted) Navigator.of(context).pop();
    } catch (e) {
      // Close loading indicator
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening report: $e'),
            backgroundColor: AppThemes.errorColor,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Try Share',
              textColor: Colors.white,
              onPressed: () => _shareReport(context, state),
            ),
          ),
        );
      }
    }
  }

  Future<void> _shareReport(BuildContext context, CustomerReportGenerated state) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      await PDFService.sharePDF(state.pdfPath, 'Customer Report - ${state.reportPeriod}');
      // Close loading indicator
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Report shared successfully'),
            backgroundColor: AppThemes.successColor,
          ),
        );
      }
    } catch (e) {
      // Close loading indicator
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing report: $e'),
            backgroundColor: AppThemes.errorColor,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _showSuccessDialog(BuildContext context, String pdfPath, String reportPeriod) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: AppThemes.successColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text('Report Generated'),
            ],
          ),
          content: const Text(
            'Your customer report has been generated successfully. What would you like to do?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _shareReport(context, CustomerReportGenerated(pdfPath: pdfPath, reportPeriod: reportPeriod));
              },
              child: Text(
                'Share',
                style: TextStyle(color: AppThemes.infoColor),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _openReport(context, CustomerReportGenerated(pdfPath: pdfPath, reportPeriod: reportPeriod));
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppThemes.primaryColor,
              ),
              child: const Text('View Report'),
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: AppThemes.errorColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text('Generation Failed'),
            ],
          ),
          content: Text(
            'Failed to generate report: $error',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _generatePDFReport(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppThemes.primaryColor,
              ),
              child: const Text('Retry'),
            ),
          ],
        );
      },
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
