/**
 * Contact Information Model
 * Represents superAdmin contact details for support
 */

class ContactInfo {
  final String phone;
  final String name;
  final String email;
  final String? supportHours;
  final String? timezone;

  const ContactInfo({
    required this.phone,
    required this.name,
    required this.email,
    this.supportHours,
    this.timezone,
  });

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      phone: json['phone'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      supportHours: json['supportHours'],
      timezone: json['timezone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'name': name,
      'email': email,
      'supportHours': supportHours,
      'timezone': timezone,
    };
  }

  // Default fallback contact info
  static const ContactInfo defaultContact = ContactInfo(
    phone: '+252 619821172',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    supportHours: '9:00 AM - 6:00 PM (GMT+3)',
    timezone: 'East Africa Time (EAT)',
  );

  @override
  String toString() {
    return 'ContactInfo(phone: $phone, name: $name, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContactInfo &&
        other.phone == phone &&
        other.name == name &&
        other.email == email &&
        other.supportHours == supportHours &&
        other.timezone == timezone;
  }

  @override
  int get hashCode {
    return phone.hashCode ^
        name.hashCode ^
        email.hashCode ^
        supportHours.hashCode ^
        timezone.hashCode;
  }
}
