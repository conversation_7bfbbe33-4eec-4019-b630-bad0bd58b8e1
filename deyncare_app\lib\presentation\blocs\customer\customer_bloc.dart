import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import 'customer_event.dart';
import 'customer_state.dart';
import 'package:deyncare_app/domain/usecases/customer/get_customers_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/create_customer_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/get_customer_details_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/update_customer_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/delete_customer_use_case.dart';
import 'package:deyncare_app/domain/usecases/customer/get_customer_debts_use_case.dart';
import 'package:deyncare_app/core/errors/failures.dart';

/// Customer BLoC - manages customer operations using clean architecture
class CustomerBloc extends Bloc<CustomerEvent, CustomerState> {
  final Logger _logger = Logger();

  // Use cases - backend aligned
  final GetCustomersUseCase _getCustomersUseCase;
  final CreateCustomerUseCase _createCustomerUseCase;
  final GetCustomerDetailsUseCase _getCustomerDetailsUseCase;
  final UpdateCustomerUseCase _updateCustomerUseCase;
  final DeleteCustomerUseCase _deleteCustomerUseCase;
  final GetCustomerDebtsUseCase _getCustomerDebtsUseCase;

  CustomerBloc({
    required GetCustomersUseCase getCustomersUseCase,
    required CreateCustomerUseCase createCustomerUseCase,
    required GetCustomerDetailsUseCase getCustomerDetailsUseCase,
    required UpdateCustomerUseCase updateCustomerUseCase,
    required DeleteCustomerUseCase deleteCustomerUseCase,
    required GetCustomerDebtsUseCase getCustomerDebtsUseCase,
  })  : _getCustomersUseCase = getCustomersUseCase,
        _createCustomerUseCase = createCustomerUseCase,
        _getCustomerDetailsUseCase = getCustomerDetailsUseCase,
        _updateCustomerUseCase = updateCustomerUseCase,
        _deleteCustomerUseCase = deleteCustomerUseCase,
        _getCustomerDebtsUseCase = getCustomerDebtsUseCase,
        super(const CustomerInitial()) {
    
    _logger.d('CustomerBloc: Initialized');

    // Register event handlers
    on<LoadCustomers>(_onLoadCustomers);
    on<CreateCustomer>(_onCreateCustomer);
    on<LoadCustomerDetails>(_onLoadCustomerDetails);
    on<UpdateCustomer>(_onUpdateCustomer);
    on<DeleteCustomer>(_onDeleteCustomer);
    on<LoadCustomerDebts>(_onLoadCustomerDebts);
    on<RefreshCustomers>(_onRefreshCustomers);
    on<ClearCustomerDetails>(_onClearCustomerDetails);
  }

  /// Load customers with pagination and filters
  Future<void> _onLoadCustomers(
    LoadCustomers event,
    Emitter<CustomerState> emit,
  ) async {
    _logger.d('CustomerBloc: Loading customers - page ${event.page}');
    
    if (event.page == 1) {
      emit(const CustomerListLoading());
    }

    final result = await _getCustomersUseCase.execute(
      page: event.page,
      limit: event.limit,
      search: event.search,
      customerType: event.customerType,
      riskLevel: event.riskLevel,
      category: event.category,
      sortBy: event.sortBy,
      ascending: event.ascending,
    );

    result.fold(
      (failure) {
        _logger.e('CustomerBloc: Failed to load customers - ${failure.message}');
        emit(CustomerListError(
          message: failure.message,
          errorCode: failure.code,
        ));
      },
      (response) {
        _logger.d('CustomerBloc: Customers loaded successfully - ${response.data.customers.length ?? 0} items');
        emit(CustomerListLoaded(response: response));
      },
    );
  }

  /// Create new customer
  Future<void> _onCreateCustomer(
    CreateCustomer event,
    Emitter<CustomerState> emit,
  ) async {
    _logger.d('CustomerBloc: Creating customer - ${event.customerName}');
    emit(const CustomerCreating());

    final result = await _createCustomerUseCase.execute(
      customerName: event.customerName,
      customerType: event.customerType,
      phone: event.phone,
      email: event.email,
      address: event.address,
      creditLimit: event.creditLimit,
      category: event.category,
      notes: event.notes,
    );

    result.fold(
      (failure) {
        _logger.e('CustomerBloc: Failed to create customer - ${failure.message}');
        emit(CustomerError(
          message: failure.message,
          errorCode: failure.code,
        ));
      },
      (response) {
        _logger.d('CustomerBloc: Customer created successfully - ${response.data?.customer.customerId}');
        emit(CustomerCreated(response));
      },
    );
  }

  /// Load customer details
  Future<void> _onLoadCustomerDetails(
    LoadCustomerDetails event,
    Emitter<CustomerState> emit,
  ) async {
    _logger.d('CustomerBloc: Loading customer details - ${event.customerId}');
    emit(const CustomerDetailsLoading());

    final result = await _getCustomerDetailsUseCase.execute(event.customerId);

    result.fold(
      (failure) {
        _logger.e('CustomerBloc: Failed to load customer details - ${failure.message}');
        emit(CustomerDetailsError(
          message: failure.message,
          errorCode: failure.code,
        ));
      },
      (response) {
        _logger.d('CustomerBloc: Customer details loaded successfully');
        emit(CustomerDetailsLoaded(response));
      },
    );
  }

  /// Update customer
  Future<void> _onUpdateCustomer(
    UpdateCustomer event,
    Emitter<CustomerState> emit,
  ) async {
    _logger.d('CustomerBloc: Updating customer - ${event.customerId}');
    emit(const CustomerUpdating());

    final result = await _updateCustomerUseCase.execute(
      customerId: event.customerId,
      customerName: event.customerName,
      email: event.email,
      phone: event.phone,
      address: event.address,
      creditLimit: event.creditLimit,
      category: event.category,
      notes: event.notes,
    );

    result.fold(
      (failure) {
        _logger.e('CustomerBloc: Failed to update customer - ${failure.message}');
        emit(CustomerError(
          message: failure.message,
          errorCode: failure.code,
        ));
      },
      (response) {
        _logger.d('CustomerBloc: Customer updated successfully');
        emit(CustomerUpdated(response));
      },
    );
  }

  /// Delete customer
  Future<void> _onDeleteCustomer(
    DeleteCustomer event,
    Emitter<CustomerState> emit,
  ) async {
    _logger.d('CustomerBloc: Deleting customer - ${event.customerId} (force: ${event.force})');
    emit(const CustomerDeleting());

    final result = await _deleteCustomerUseCase.execute(
      event.customerId,
      force: event.force,
    );

    result.fold(
      (failure) {
        _logger.e('CustomerBloc: Failed to delete customer - ${failure.message}');
        emit(CustomerError(
          message: failure.message,
          errorCode: failure.code,
        ));
      },
      (success) {
        _logger.d('CustomerBloc: Customer deleted successfully');
        emit(CustomerDeleted(event.customerId));
      },
    );
  }

  /// Load customer debts
  Future<void> _onLoadCustomerDebts(
    LoadCustomerDebts event,
    Emitter<CustomerState> emit,
  ) async {
    _logger.d('CustomerBloc: Loading customer debts - ${event.customerId}');

    final result = await _getCustomerDebtsUseCase.execute(
      customerId: event.customerId,
      includeCompleted: event.includeCompleted,
    );

    result.fold(
      (failure) {
        _logger.e('CustomerBloc: Failed to load customer debts - ${failure.message}');
        emit(CustomerError(
          message: failure.message,
          errorCode: failure.code,
        ));
      },
      (response) {
        _logger.d('CustomerBloc: Customer debts loaded successfully');
        emit(CustomerDebtsLoaded(response));
      },
    );
  }

  /// Refresh customers list
  Future<void> _onRefreshCustomers(
    RefreshCustomers event,
    Emitter<CustomerState> emit,
  ) async {
    _logger.d('CustomerBloc: Refreshing customers');
    
    if (state is CustomerListLoaded) {
      final currentState = state as CustomerListLoaded;
      emit(currentState.copyWith(isRefreshing: true));
    }

    // Reload first page
    add(const LoadCustomers(page: 1));
  }

  /// Clear customer details
  void _onClearCustomerDetails(
    ClearCustomerDetails event,
    Emitter<CustomerState> emit,
  ) {
    _logger.d('CustomerBloc: Clearing customer details');
    emit(const CustomerInitial());
  }

  /// Helper method to get error message from failure
  String _getErrorMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ValidationFailure:
        return failure.message;
      case NetworkFailure:
        return 'Network error. Please check your connection.';
      case ServerFailure:
        return 'Server error. Please try again later.';
      default:
        return 'An unexpected error occurred.';
    }
  }
} 