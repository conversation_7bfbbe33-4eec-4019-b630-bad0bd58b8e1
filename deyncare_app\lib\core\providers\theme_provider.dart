import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';

/// Theme provider for managing dark/light mode switching
class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  
  ThemeMode _themeMode = ThemeMode.light;
  bool _isInitialized = false;
  
  ThemeMode get themeMode => _themeMode;
  
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  
  bool get isInitialized => _isInitialized;
  
  /// Get light theme
  ThemeData get lightTheme => AppThemes.lightTheme;
  
  /// Get dark theme  
  ThemeData get darkTheme => AppThemes.darkTheme;
  
  /// Initialize theme from shared preferences
  Future<void> initializeTheme() async {
    if (_isInitialized) {
      debugPrint('🎨 Theme already initialized: $_themeMode');
      return;
    }
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themeKey);
      
      if (savedTheme != null) {
        _themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == savedTheme,
          orElse: () => ThemeMode.light,
        );
      }
      
      _isInitialized = true;
      debugPrint('🎨 Theme initialized: $_themeMode');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Failed to initialize theme: $e');
      _themeMode = ThemeMode.light;
      _isInitialized = true;
      notifyListeners();
    }
  }
  
  /// Set specific theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      debugPrint('🎨 Theme set to: $_themeMode');
      await _saveTheme();
      notifyListeners();
    }
  }
  
  /// Set dark mode
  Future<void> setDarkMode(bool isDark) async {
    final newMode = isDark ? ThemeMode.dark : ThemeMode.light;
    if (_themeMode != newMode) {
      debugPrint('🎨 Setting dark mode: $isDark -> $newMode');
      _themeMode = newMode;
      await _saveTheme();
      notifyListeners();
    }
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final newMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    
    if (_themeMode != newMode) {
      _themeMode = newMode;
      debugPrint('🎨 Theme toggled to: $_themeMode');
      await _saveTheme();
      notifyListeners();
    }
  }
  
  /// Save theme preference to shared preferences
  Future<void> _saveTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, _themeMode.toString());
      debugPrint('🎨 Theme saved: $_themeMode');
    } catch (e) {
      debugPrint('❌ Failed to save theme: $e');
    }
  }
} 