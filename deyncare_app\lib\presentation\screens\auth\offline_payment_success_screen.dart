import 'dart:async';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:deyncare_app/core/constants/app_themes.dart';
import 'package:deyncare_app/core/utils/theme_utils.dart';
import 'package:deyncare_app/domain/models/user.dart';
import 'package:deyncare_app/core/routes/app_router.dart';
import 'package:deyncare_app/data/models/contact_info.dart';

/// Screen displayed after successful offline payment submission
class OfflinePaymentSuccessScreen extends StatefulWidget {
  final User user;
  final String planName;
  final double planAmount;

  const OfflinePaymentSuccessScreen({
    super.key,
    required this.user,
    required this.planName,
    required this.planAmount,
  });

  @override
  State<OfflinePaymentSuccessScreen> createState() => _OfflinePaymentSuccessScreenState();
}

class _OfflinePaymentSuccessScreenState extends State<OfflinePaymentSuccessScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  Timer? _timer;
  int _countdown = 45; // Extended countdown for better UX

  // Contact info state
  final ContactInfo _contactInfo = ContactInfo.defaultContact;
  bool _showStatusDetails = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    // Start animations
    _animationController.forward();

    // Contact info is now using default fallback

    // Start countdown timer
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
      } else {
        _timer?.cancel();
        AppRouter.navigateToLogin(context);
      }
    });
  }

  /// Toggle status details visibility
  void _toggleStatusDetails() {
    setState(() {
      _showStatusDetails = !_showStatusDetails;
    });
  }

  /// Launch email app with pre-filled support email
  Future<void> _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: _contactInfo.email,
      query: 'subject=Offline Payment Status Inquiry - ${widget.user.fullName}',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  /// Launch phone dialer
  Future<void> _launchPhone() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: _contactInfo.phone);

    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  /// Build status item widget
  Widget _buildStatusItem(IconData icon, String title, String subtitle, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppThemes.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppThemes.primaryColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppThemes.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: AppThemes.primaryColor,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build detail item widget
  Widget _buildDetailItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6, right: 12),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: AppThemes.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppThemes.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Success Icon
                ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: AppThemes.successColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppThemes.successColor,
                        width: 3,
                      ),
                    ),
                    child: Icon(
                      Icons.check_circle_outline,
                      size: 60,
                      color: AppThemes.successColor,
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Success Title
                Text(
                  'Offline Payment Submitted!',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppThemes.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Success Message
                Text(
                  'Your offline payment details have been successfully submitted for review.',
                  style: TextStyle(
                    fontSize: 16,
                    color: ThemeUtils.getTextColor(context, type: TextColorType.secondary),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Enhanced Status Communication Card
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppThemes.primaryColor.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.pending_actions,
                            color: AppThemes.primaryColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Registration Status: Pending Approval',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppThemes.primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Status Details
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppThemes.dividerColor),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildStatusItem(
                              Icons.schedule,
                              'Expected Timeline',
                              'Usually approved within 24-48 hours',
                              'Our team reviews payments during business hours',
                            ),
                            const SizedBox(height: 12),
                            _buildStatusItem(
                              Icons.email_outlined,
                              'Email Notification',
                              'You\'ll receive an email when approved',
                              'Check your inbox (including spam folder)',
                            ),
                            const SizedBox(height: 12),
                            _buildStatusItem(
                              Icons.verified_user,
                              'Approval Process',
                              'SuperAdmin will verify your payment',
                              'Once approved, your account will be activated automatically',
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Action Button
                      GestureDetector(
                        onTap: _toggleStatusDetails,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                          decoration: BoxDecoration(
                            color: AppThemes.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppThemes.primaryColor.withOpacity(0.3)),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _showStatusDetails ? Icons.expand_less : Icons.expand_more,
                                color: AppThemes.primaryColor,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _showStatusDetails ? 'Hide Details' : 'Show More Details',
                                style: TextStyle(
                                  color: AppThemes.primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Expandable Details
                      if (_showStatusDetails) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppThemes.dividerColor),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Approval Process Details',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppThemes.textPrimaryColor,
                                ),
                              ),
                              const SizedBox(height: 12),
                              _buildDetailItem('1. Payment Verification', 'SuperAdmin reviews your payment proof and details'),
                              _buildDetailItem('2. Account Activation', 'Your shop account is activated upon approval'),
                              _buildDetailItem('3. Email Confirmation', 'You receive login credentials and welcome email'),
                              _buildDetailItem('4. Ready to Use', 'Start using DeynCare immediately after approval'),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Payment Details Card
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppThemes.cardColor,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppThemes.dividerColor),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.store,
                            color: AppThemes.primaryColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Payment Summary',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppThemes.textPrimaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildDetailRow('Plan', widget.planName),
                      _buildDetailRow('Amount', '\$${widget.planAmount.toStringAsFixed(2)}'),
                      _buildDetailRow('Status', 'Pending Review'),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // Next Steps Card
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppThemes.infoColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppThemes.infoColor.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppThemes.infoColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'What happens next?',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppThemes.infoColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildNextStepItem('1. SuperAdmin will verify your payment proof and details'),
                      _buildNextStepItem('2. You\'ll receive an email with login credentials when approved'),
                      _buildNextStepItem('3. Your shop account will be activated automatically'),
                      _buildNextStepItem('4. Start using DeynCare immediately after approval'),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppThemes.warningColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: AppThemes.warningColor.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.schedule,
                              color: AppThemes.warningColor,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Review typically takes 1-2 business days',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppThemes.warningColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // Enhanced Contact Support Section
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppThemes.successColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppThemes.successColor.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.support_agent,
                            color: AppThemes.successColor,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Need Help or Have Questions?',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppThemes.successColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Contact Information
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppThemes.dividerColor),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'Contact ${_contactInfo.name}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppThemes.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 12),

                            // Phone Contact
                            GestureDetector(
                              onTap: _launchPhone,
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: AppThemes.primaryColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: AppThemes.primaryColor.withValues(alpha: 0.3)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.phone,
                                      color: AppThemes.primaryColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Call Now',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                              color: AppThemes.primaryColor,
                                            ),
                                          ),
                                          Text(
                                            _contactInfo.phone,
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: AppThemes.primaryColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      color: AppThemes.primaryColor,
                                      size: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            const SizedBox(height: 12),

                            // Email Contact
                            GestureDetector(
                              onTap: _launchEmail,
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: AppThemes.successColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: AppThemes.successColor.withValues(alpha: 0.3)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.email,
                                      color: AppThemes.successColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Send Email',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                              color: AppThemes.successColor,
                                            ),
                                          ),
                                          Text(
                                            _contactInfo.email,
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: AppThemes.successColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      color: AppThemes.successColor,
                                      size: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            if (_contactInfo.supportHours != null) ...[
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.access_time,
                                      color: Colors.grey[600],
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'Support Hours: ${_contactInfo.supportHours}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // Countdown and Login Button
                Text(
                  'Redirecting to login in $_countdown seconds...',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeUtils.getTextColor(context, type: TextColorType.hint),
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      _timer?.cancel();
                      AppRouter.navigateToLogin(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppThemes.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Go to Login now',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppThemes.textSecondaryColor,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppThemes.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6, right: 12),
            decoration: BoxDecoration(
              color: AppThemes.infoColor,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: AppThemes.textSecondaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
