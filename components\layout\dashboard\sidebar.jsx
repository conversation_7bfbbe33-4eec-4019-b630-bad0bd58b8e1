"use client";

import { useRef, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/ui/logo";
import { useResponsive } from "@/hooks/use-responsive";
import {
  LayoutDashboard,
  Users,
  Settings,
  LogOut,
  ShoppingBag,
  FileText,
  Building2,
  Wallet,
  Calendar,
  Shield,
  ChevronRight,
  Brain,
  Package,
  User,
  Smartphone
} from "lucide-react";
import { useAuth } from "@/contexts/auth-context";

export function Sidebar({ className }) {
  const pathname = usePathname();
  const { isMobile } = useResponsive();
const { user, logout, isSuperAdmin } = useAuth();
const sidebarRef = useRef(null);

  // Main navigation items
  const mainNavItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      title: "User Management",
      href: "/dashboard/users",
      icon: <Users className="h-5 w-5" />,
    },
    {
      title: "Shop Management",
      href: "/dashboard/shops",
      icon: <Building2 className="h-5 w-5" />,
    },
    {
      title: "Plans",
      href: "/dashboard/plans",
      icon: <Package className="h-5 w-5" />,
    },
    {
      title: "Subscriptions",
      href: "/dashboard/subscriptions",
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      title: "Payment Transactions",
      href: "/dashboard/payment-transactions",
      icon: <Wallet className="h-5 w-5" />,
      description: "Manage subscription payment transactions"
    },
    // App Management - SuperAdmin only
    ...(isSuperAdmin() ? [{
      title: "App Management",
      href: "/dashboard/apps",
      icon: <Smartphone className="h-5 w-5" />,
    }] : []),
  ];

  // Secondary navigation items
  const secondaryNavItems = [
    {
      title: "Profile",
      href: "/dashboard/profile",
      icon: <User className="h-5 w-5" />,
    },
    {
      title: "System Settings",
      href: "/dashboard/settings",
      icon: <Settings className="h-5 w-5" />,
      subItems: [
        {
          title: "General",
          href: "/dashboard/settings/general",
        },
        {
          title: "Security",
          href: "/dashboard/settings/security",
        },
        {
          title: "Payment Methods",
          href: "/dashboard/settings/payment-methods",
        },
        {
          title: "ML Settings",
          href: "/dashboard/settings/ml-settings",
        },
        // Shop configuration removed - business rules now managed by individual shops
        {
          title: "System Logs",
          href: "/dashboard/settings/system-logs",
        },
        {
          title: "Notifications",
          href: "/dashboard/settings/notifications",
        },
      ]
    },

  ];

  // Function to handle logout
  const handleLogout = () => {
    logout();
  };

  // Check if a route is active
  const isRouteActive = (href) => {
    if (href === '/dashboard') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <aside
      ref={sidebarRef}
      className={cn(
        "relative flex flex-col h-full w-64 bg-background border-r border-border z-30",
        className
      )}
    >
      {/* Logo and brand area */}
      <div className="p-4 border-b border-border flex justify-between items-center">
        <div className="overflow-hidden flex-1">
          <Logo variant="full" size="sm" showText={true} href="/dashboard" />
        </div>
      </div>

      {/* Navigation items - Optimized scroll container */}
      <nav className="flex-1 min-h-0 py-4 px-2 overflow-y-auto scrollbar-hide">
        <div className="space-y-1">
          {mainNavItems.map((item) => {
            const isActive = isRouteActive(item.href);
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:bg-muted hover:text-foreground"
                )}
              >
                <span className="relative">
                  {item.icon}
                  {item.badge && (
                    <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                      {item.badge}
                    </span>
                  )}
                </span>
                <span className="ml-3">{item.title}</span>
              </Link>
            );
          })}
        </div>

        <div className="my-4 border-t border-border" />

        <div className="space-y-1">
          {secondaryNavItems.map((item) => {
            const isActive = isRouteActive(item.href);
            return (
              <div key={item.href}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  )}
                >
                  {item.icon}
                  <span className="ml-3">{item.title}</span>
                  {item.subItems && <ChevronRight className="ml-auto h-4 w-4" />}
                </Link>
                
                {item.subItems && isActive && (
                  <div className="ml-6 mt-1 space-y-1">
                    {item.subItems.map((subItem) => {
                      const isSubActive = pathname === subItem.href;
                      return (
                        <Link
                          key={subItem.href}
                          href={subItem.href}
                          className={cn(
                            "flex items-center rounded-md px-3 py-1.5 text-sm transition-colors",
                            isSubActive
                              ? "bg-primary/10 text-primary font-medium"
                              : "text-muted-foreground hover:bg-muted hover:text-foreground"
                          )}
                        >
                          <span className="h-1 w-1 rounded-full bg-current mr-2"></span>
                          {subItem.title}
                        </Link>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </nav>



      {/* Version info */}
      <div className="px-3 py-2 text-xs text-muted-foreground">
        <p className="text-center">Deyncare v1.0.0</p>
      </div>
    </aside>
  );
}
