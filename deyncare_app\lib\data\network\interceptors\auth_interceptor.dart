import 'dart:async';
import 'package:dio/dio.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart'; // Added for kDebugMode

/// Interceptor to handle authentication tokens and refresh logic
class AuthInterceptor extends Interceptor {
  final TokenManager _tokenManager;
  final Dio _dio;
  final String _deviceId;
  final Logger _logger = Logger();
  
  // Prevent multiple simultaneous refresh attempts
  Completer<void>? _refreshCompleter;
  
  AuthInterceptor({
    required TokenManager tokenManager,
    required Dio dio,
    required String deviceId,
  }) : 
    _tokenManager = tokenManager,
    _dio = dio,
    _deviceId = deviceId;
  
  @override
  void onRequest(
    RequestOptions options, 
    RequestInterceptorHandler handler,
  ) async {
    // Debug: Log original path
    final originalPath = options.path;
    
    // Format URL correctly - ensure it starts with /
    if (!options.path.startsWith('/')) {
      options.path = '/${options.path}';
    }
    
    // Check if auth endpoint (needs /auth prefix)
    // EXCLUDE FCM endpoints and REGISTRATION endpoints from auth detection
    // Registration endpoints (/register/*) are mounted directly at /api/register/* NOT /api/auth/register/*
    final isAuthEndpoint = !options.path.contains('/fcm/') && 
                          !options.path.contains('/register') && (
                          options.path.contains('/login') || 
                          options.path.contains('/forgot-password') ||
                          options.path.contains('/reset-password') ||
                          options.path.contains('/verify-email') ||
                          options.path.contains('/refresh-token') ||
                          options.path.contains('/logout') ||
                          options.path.contains('/check-email') ||
                          options.path.contains('/me'));
    
    // Debug: Log classification
    if (kDebugMode) {
      print('🔍 AuthInterceptor Debug:');
      print('   Original path: $originalPath');
      print('   Formatted path: ${options.path}');
      print('   Is auth endpoint: $isAuthEndpoint');
      print('   Base URL: ${options.baseUrl}');
      
      // Special debugging for profile update endpoint
      if (options.path.contains('/me')) {
        print('🔐 PROFILE UPDATE ENDPOINT DETECTED!');
        print('   🔐 Full path: ${options.path}');
        print('   🔐 Method: ${options.method}');
        print('   🔐 Current headers: ${options.headers}');
      }
    }
    
    // ONLY add /auth prefix for authentication endpoints
    // DO NOT add /api prefix since base URL already contains it!
    // DO NOT add /auth prefix to FCM endpoints!
    if (isAuthEndpoint && !options.path.startsWith('/auth')) {
      if (options.path.startsWith('/')) {
        options.path = '/auth${options.path}';
      } else {
        options.path = '/auth/${options.path}';
      }
    }
    
    // Debug: Log final path
    if (kDebugMode) {
      print('   Final path: ${options.path}');
      print('   Final URL: ${options.baseUrl}${options.path}');
    }
    
    // Add device identifier for token management (especially for multi-device logout)
    if (options.path.contains('/auth/login') || options.path.contains('/auth/register')) {
      // Inject device ID for login/register requests
      if (options.data is Map) {
        try {
          Map<String, dynamic> data;
          if (options.data is Map<String, dynamic>) {
            data = Map<String, dynamic>.from(options.data as Map<String, dynamic>);
          } else {
            data = Map<String, dynamic>.from(options.data as Map);
          }
          data['deviceName'] = _deviceId;
          options.data = data;
        } catch (e) {
          _logger.e('AuthInterceptor: Error processing request data: $e');
          // Continue without modifying data if casting fails
        }
      }
    }
    
    // Skip adding token for public endpoints
    final isPublicEndpoint = options.path.contains('/auth/login') ||
                          options.path.contains('/auth/forgot-password') ||
                          options.path.contains('/auth/reset-password') ||
                          options.path.contains('/auth/verify-email') ||
                          options.path.contains('/auth/resend-verification') ||  // Added this line
                          options.path.contains('/auth/refresh-token') ||
                          options.path.contains('/auth/check-email') ||
                          // Registration endpoints (except /register/pay) are public
                          (options.path.contains('/register') && !options.path.contains('/register/pay'));
    
    // Special handling for /register/pay - this endpoint requires authentication
    final isAuthenticatedRegisterEndpoint = options.path.contains('/register/pay');
    
    // Debug: Log endpoint classification
    if (kDebugMode) {
      print('🔐 Endpoint Classification:');
      print('   🔐 Is public endpoint: $isPublicEndpoint');
      print('   🔐 Is authenticated register endpoint: $isAuthenticatedRegisterEndpoint');
      print('   🔐 Will add token: ${!isPublicEndpoint || isAuthenticatedRegisterEndpoint}');
      
      if (options.path.contains('/me')) {
        print('🔐 PROFILE ENDPOINT - DETAILED CHECK:');
        print('   🔐 Contains /auth/login: ${options.path.contains('/auth/login')}');
        print('   🔐 Contains /auth/forgot-password: ${options.path.contains('/auth/forgot-password')}');
        print('   🔐 Contains /auth/reset-password: ${options.path.contains('/auth/reset-password')}');
        print('   🔐 Contains /auth/verify-email: ${options.path.contains('/auth/verify-email')}');
        print('   🔐 Contains /auth/refresh-token: ${options.path.contains('/auth/refresh-token')}');
        print('   🔐 Contains /register: ${options.path.contains('/register')}');
        print('   🔐 FINAL: isPublicEndpoint = $isPublicEndpoint');
      }
    }
    
    if (!isPublicEndpoint || isAuthenticatedRegisterEndpoint) {
      await _ensureValidTokenInRequest(options);
    } else {
      _logger.d('AuthInterceptor: Skipping token for public endpoint: ${options.path}');
    }
    
    // Debug: Log final headers for authentication debugging
    if (kDebugMode) {
      final hasAuthHeader = options.headers['Authorization'] != null;
      print('   Has Authorization header: $hasAuthHeader');
      if (hasAuthHeader) {
        final authHeader = options.headers['Authorization'] as String;
        final tokenPreview = authHeader.length > 20 ? '${authHeader.substring(0, 20)}...' : authHeader;
        print('   Auth header: $tokenPreview');
      }
    }
    
    // Log the final request path and method for debugging
    _logger.d('AuthInterceptor: Final URL: ${options.method} ${options.baseUrl}${options.path}');
    
    return handler.next(options);
  }
  
  /// Ensure request has a valid token, handling missing or expired tokens
  Future<void> _ensureValidTokenInRequest(RequestOptions options) async {
    try {
      if (kDebugMode) {
        print('🔐 AuthInterceptor: Ensuring valid token for ${options.path}');
      }
      
      // First check if we already have a token in the request headers
      String? currentToken = _extractTokenFromHeaders(options.headers);
      
      if (kDebugMode) {
        print('🔐 Token from headers: ${currentToken != null ? "Found (${currentToken.length} chars)" : "Not found"}');
      }
      
      // If no token in headers, try to get from storage
      if (currentToken == null || currentToken.isEmpty) {
        currentToken = await _tokenManager.getAccessToken();
        
        if (kDebugMode) {
          print('🔐 Token from storage: ${currentToken != null ? "Found (${currentToken.length} chars)" : "Not found"}');
        }
        
        if (currentToken != null && currentToken.isNotEmpty) {
          // Validate token integrity before using it
          final isValid = await _tokenManager.validateTokenIntegrity(currentToken);
          if (kDebugMode) {
            print('🔐 Token validation: ${isValid ? "Valid" : "Invalid"}');
          }
          
          if (isValid) {
            options.headers['Authorization'] = 'Bearer $currentToken';
            _logger.d('AuthInterceptor: Added valid token from storage to request: ${options.path}');
            
            if (kDebugMode) {
              print('🔐 ✅ Authorization header SET for ${options.path}');
            }
          } else {
            _logger.w('AuthInterceptor: Token from storage is invalid, attempting refresh');
            currentToken = null; // Clear invalid token
          }
        }
      }
      
      // If we have a token, check if it should be refreshed proactively
      if (currentToken != null && currentToken.isNotEmpty) {
        final shouldRefresh = await _tokenManager.shouldRefreshToken();
        
        if (kDebugMode) {
          print('🔐 Should refresh token: $shouldRefresh');
        }
        
        if (shouldRefresh) {
          _logger.d('AuthInterceptor: Token should be refreshed for request: ${options.path}, attempting refresh');
          await _refreshTokenIfNeeded();
          
          // Get the refreshed token and update request headers
          final refreshedToken = await _tokenManager.getAccessToken();
          if (refreshedToken != null && refreshedToken.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $refreshedToken';
            _logger.d('AuthInterceptor: Updated request with refreshed token: ${options.path}');
            
            if (kDebugMode) {
              print('🔐 ✅ Authorization header UPDATED with refreshed token for ${options.path}');
            }
          } else {
            _logger.w('AuthInterceptor: No token available after refresh attempt for: ${options.path}');
            
            if (kDebugMode) {
              print('🔐 ❌ No token available after refresh for ${options.path}');
            }
          }
        }
      } else {
        // No valid token available, try to refresh if we have a refresh token
        final hasRefreshToken = await _tokenManager.hasValidRefreshToken();
        
        if (kDebugMode) {
          print('🔐 Has valid refresh token: $hasRefreshToken');
        }
        
        if (hasRefreshToken) {
          _logger.d('AuthInterceptor: No access token but valid refresh token exists, attempting refresh');
          await _refreshTokenIfNeeded();
          
          final newToken = await _tokenManager.getAccessToken();
          if (newToken != null && newToken.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $newToken';
            _logger.d('AuthInterceptor: Added refreshed token to request: ${options.path}');
            
            if (kDebugMode) {
              print('🔐 ✅ Authorization header SET with new refreshed token for ${options.path}');
            }
          } else {
            _logger.w('AuthInterceptor: Failed to obtain token after refresh for: ${options.path}');
            
            if (kDebugMode) {
              print('🔐 ❌ Failed to get token after refresh for ${options.path}');
            }
          }
        } else {
          _logger.w('AuthInterceptor: No valid tokens available for authenticated request: ${options.path}');
          
          if (kDebugMode) {
            print('🔐 ❌ NO VALID TOKENS AVAILABLE for ${options.path}');
            print('🔐 This will likely result in 401/403 authentication error');
            _debugTokenStatus();
          }

          // For critical endpoints, trigger authentication state recovery
          if (_isCriticalEndpoint(options.path)) {
            _triggerAuthStateRecovery();
          }

          // Log token loss for debugging
          _logger.e('AuthInterceptor: No tokens available for authenticated endpoint: ${options.path}');
          _logger.e('AuthInterceptor: This may indicate premature token clearing or session validation issues');
        }
      }
      
      // Final debug check
      if (kDebugMode) {
        final finalHasAuth = options.headers.containsKey('Authorization');
        print('🔐 Final check - Has Authorization header: $finalHasAuth');
        if (finalHasAuth) {
          final authHeader = options.headers['Authorization'] as String;
          final preview = authHeader.length > 30 ? '${authHeader.substring(0, 30)}...' : authHeader;
          print('🔐 Final Authorization header: $preview');
        }
      }
    } catch (e) {
      _logger.e('AuthInterceptor: Error ensuring valid token for request: $e');
      
      if (kDebugMode) {
        print('🔐 ❌ ERROR in token handling: $e');
      }
      // Continue with request even if token handling fails
    }
  }
  
  /// Extract token from Authorization header
  String? _extractTokenFromHeaders(Map<String, dynamic> headers) {
    final authHeader = headers['Authorization'];
    if (authHeader != null && authHeader is String && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7); // Remove 'Bearer ' prefix
    }
    return null;
  }

  /// Debug token status for troubleshooting
  Future<void> _debugTokenStatus() async {
    try {
      final tokenInfo = await _tokenManager.getTokenInfo();
      print('🔐 DEBUG Token Status:');
      print('  - Has Access Token: ${tokenInfo['hasAccessToken']}');
      print('  - Has Refresh Token: ${tokenInfo['hasRefreshToken']}');
      print('  - Is Expired: ${tokenInfo['isExpired']}');
      print('  - Should Refresh: ${tokenInfo['shouldRefresh']}');
      print('  - Time Until Expiry: ${tokenInfo['timeUntilExpiry']} minutes');
      print('  - Access Token Valid: ${tokenInfo['accessTokenValid']}');
      print('  - Refresh Token Valid: ${tokenInfo['refreshTokenValid']}');
    } catch (e) {
      print('🔐 DEBUG Error getting token status: $e');
    }
  }

  /// Check if endpoint is critical and requires immediate auth recovery
  bool _isCriticalEndpoint(String path) {
    return path.contains('/register/pay') ||
           path.contains('/user/profile') ||
           path.contains('/shop/') ||
           path.contains('/payment/');
  }

  /// Trigger authentication state recovery
  void _triggerAuthStateRecovery() {
    // This could trigger a navigation to login screen or show auth dialog
    // For now, just log the issue
    _logger.w('AuthInterceptor: Critical endpoint accessed without valid tokens - auth recovery needed');
    if (kDebugMode) {
      print('🔐 ⚠️ CRITICAL: Authentication state recovery needed');
    }
  }

  /// Ensure we have a valid token, refresh if necessary
  Future<void> _ensureValidToken() async {
    try {
      // Check if token is expired or will expire soon
      final isExpired = await _tokenManager.isTokenExpired();
      
      if (isExpired) {
        _logger.d('AuthInterceptor: Token expired or expiring soon, attempting refresh');
        await _refreshTokenIfNeeded();
      }
    } catch (e) {
      _logger.e('AuthInterceptor: Error checking token expiry: $e');
    }
  }
  
  /// Refresh token if needed, with concurrency protection
  Future<void> _refreshTokenIfNeeded() async {
    // If already refreshing, wait for it to complete
    if (_refreshCompleter != null) {
      _logger.d('AuthInterceptor: Token refresh already in progress, waiting...');
      await _refreshCompleter!.future;
      return;
    }
    
    // Start refresh process
    _refreshCompleter = Completer<void>();
    
    try {
      final refreshToken = await _tokenManager.getRefreshToken();
      
      if (refreshToken == null || refreshToken.isEmpty) {
        _logger.w('AuthInterceptor: No refresh token available');
        _refreshCompleter!.complete();
        _refreshCompleter = null;
        return;
      }
      
      _logger.d('AuthInterceptor: Attempting to refresh token');
      
      final response = await _dio.post(
        '/auth/refresh-token',
        data: {'refreshToken': refreshToken},
        options: Options(
          headers: {'Authorization': 'Bearer $refreshToken'},
          extra: {'skipAuthInterceptor': true}, // Prevent recursive calls
        ),
      );
      
      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        
        if (data.containsKey('data') && 
            data['data'] is Map<String, dynamic> &&
            data['data'].containsKey('accessToken')) {
          
          _logger.i('AuthInterceptor: Token refresh successful');
          
          // Backend reuses refresh token, so keep the existing one if not provided
          final newRefreshToken = data['data'].containsKey('refreshToken') 
              ? data['data']['refreshToken'] 
              : refreshToken;
          
          await _tokenManager.saveTokens(
            accessToken: data['data']['accessToken'],
            refreshToken: newRefreshToken,
          );
        } else {
          _logger.e('AuthInterceptor: Refresh response missing required data');
          await _tokenManager.clearTokens();
        }
      } else {
        _logger.e('AuthInterceptor: Refresh failed with status: ${response.statusCode}');
        await _tokenManager.clearTokens();
      }
    } catch (e) {
      _logger.e('AuthInterceptor: Error during proactive token refresh: $e');
      await _tokenManager.clearTokens();
    } finally {
      _refreshCompleter!.complete();
      _refreshCompleter = null;
    }
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Skip if this request was marked to skip auth interceptor
    if (err.requestOptions.extra.containsKey('skipAuthInterceptor')) {
      return handler.next(err);
    }
    
    // Don't intercept 401 errors for login endpoints - let them show original error message
    if (err.response?.statusCode == 401 && err.requestOptions.path.contains('/auth/login')) {
      _logger.d('AuthInterceptor: Skipping token refresh for login 401 error');
      return handler.next(err);
    }
    
    // Check for token expiration
    if (err.response?.statusCode == 401) {
      _logger.d('AuthInterceptor: 401 Unauthorized error for ${err.requestOptions.path}');
      
      // Get refresh token
      final refreshToken = await _tokenManager.getRefreshToken();
      
      // If we have a refresh token, try to refresh
      if (refreshToken != null && refreshToken.isNotEmpty) {
        _logger.d('AuthInterceptor: Attempting to refresh token after 401');
        
        try {
          // Try to refresh the token
          final response = await _dio.post(
            '/auth/refresh-token',
            data: {'refreshToken': refreshToken},
            options: Options(
              headers: {'Authorization': 'Bearer $refreshToken'},
              extra: {'skipAuthInterceptor': true},
            ),
          );
          
          // Check if refresh was successful
          if (response.statusCode == 200 && response.data != null) {
            final data = response.data as Map<String, dynamic>;
            
            if (data.containsKey('data') && 
                data['data'] is Map<String, dynamic> &&
                data['data'].containsKey('accessToken')) {
              
              _logger.d('AuthInterceptor: Token refresh successful after 401');
              
              // Backend reuses refresh token, so keep the existing one if not provided
              final newRefreshToken = data['data'].containsKey('refreshToken') 
                  ? data['data']['refreshToken'] 
                  : refreshToken;
              
              // Save new tokens
              await _tokenManager.saveTokens(
                accessToken: data['data']['accessToken'],
                refreshToken: newRefreshToken,
              );
              
              // Retry the original request with new token
              final accessToken = data['data']['accessToken'];
              
              // Clone the original request
              final opts = Options(
                method: err.requestOptions.method,
                headers: {
                  ...err.requestOptions.headers,
                  'Authorization': 'Bearer $accessToken'
                },
              );
              
              _logger.d('AuthInterceptor: Retrying request with new token: ${err.requestOptions.path}');
              
              // Create new request with updated token
              final response = await _dio.request<dynamic>(
                err.requestOptions.path,
                data: err.requestOptions.data,
                queryParameters: err.requestOptions.queryParameters,
                options: opts,
              );
              
              // Return the new response
              return handler.resolve(response);
            } else {
              _logger.e('AuthInterceptor: Refresh token response missing required data');
              await _tokenManager.clearTokens();
            }
          } else {
            _logger.e('AuthInterceptor: Refresh token request failed with status: ${response.statusCode}');
            await _tokenManager.clearTokens();
          }
        } catch (e) {
          // If refresh fails, clear tokens and proceed with error
          _logger.e('AuthInterceptor: Error during token refresh: $e');
          await _tokenManager.clearTokens();
        }
      } else {
        _logger.w('AuthInterceptor: No refresh token available');
        await _tokenManager.clearTokens();
      }
    }
    
    // Pass error to next handler
    return handler.next(err);
  }
}
