import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:http_parser/http_parser.dart';

import 'package:deyncare_app/core/config/env_config.dart';
import 'package:deyncare_app/data/network/handlers/connectivity_handler.dart';
import 'package:deyncare_app/data/network/handlers/error_handler.dart';
import 'package:deyncare_app/data/network/handlers/response_handler.dart';
import 'package:deyncare_app/data/network/handlers/retry_handler.dart';
import 'package:deyncare_app/data/network/interceptors/auth_interceptor.dart';
import 'package:deyncare_app/data/network/interceptors/error_interceptor.dart';
import 'package:deyncare_app/data/network/interceptors/logging_interceptor.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';

/// HTTP client implementation using Dio for network requests
class DioClient {
  final Dio _dio;
  final TokenManager _tokenManager;
  final ConnectivityHandler _connectivityHandler;
  final RetryHandler _retryHandler;
  
  // Device identifier for multi-device support
  final String _deviceId;
  
  // Track if tokens are loaded
  bool _tokensLoaded = false;
  
  // Track if client is fully initialized
  bool _isInitialized = false;
  
  DioClient({
    Dio? dio,
    TokenManager? tokenManager,
    ConnectivityHandler? connectivityHandler,
    RetryHandler? retryHandler,
    String? deviceId,
  }) : 
    _tokenManager = tokenManager ?? TokenManager(),
    _connectivityHandler = connectivityHandler ?? ConnectivityHandler(),
    _retryHandler = retryHandler ?? const RetryHandler(),
    _deviceId = deviceId ?? 'deyncare_mobile_${DateTime.now().millisecondsSinceEpoch}',
    _dio = dio ?? Dio(BaseOptions(
      baseUrl: EnvConfig.baseApiUrl,
      connectTimeout: const Duration(seconds: 15),  // Reduced from 60 to 15 seconds
      receiveTimeout: const Duration(seconds: 15),  // Reduced from 60 to 15 seconds
      sendTimeout: const Duration(seconds: 15),     // Reduced from 60 to 15 seconds
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      validateStatus: (status) {
        // Accept all status codes to handle errors manually
        return true;
      },
    )) {
    // Note: We cannot await init() in constructor, so we mark as not initialized
    // The injection container will call init() explicitly after registration
    if (kDebugMode) {
      print('🔧 DioClient: Constructor completed, waiting for init() call');
    }
  }
  
  /// Initialize the client with interceptors and connectivity
  Future<void> init() async {
    // Initialize connectivity service
    await _connectivityHandler.initialize();
    
    // Make sure the URL is up to date with the current environment
    _dio.options.baseUrl = EnvConfig.baseApiUrl;
    
    // Log the API base URL and environment in debug mode
    if (kDebugMode) {
      print('🔗 API Client initialized');
      print('🌍 Environment: ${EnvConfig.environmentName.toUpperCase()}');
      print('📡 Base URL: ${_dio.options.baseUrl}');
      print('🛠️ Debug Mode: $kDebugMode');
    }
    
    // Load stored tokens before adding interceptors
    await _loadStoredTokens();
    
    // Add custom interceptors
    if (kDebugMode) {
      _dio.interceptors.add(LoggingInterceptor());
    }
    
    // Add auth interceptor
    _dio.interceptors.add(AuthInterceptor(
      tokenManager: _tokenManager,
      dio: _dio,
      deviceId: _deviceId,
    ));
    
    // Add error interceptor
    _dio.interceptors.add(ErrorInterceptor());
    
    // Debug: Log final initialization status
    if (kDebugMode) {
      print('🔧 DioClient: Interceptors added successfully');
      print('🔑 DioClient: Tokens loaded: $_tokensLoaded');
      print('📋 DioClient: Authorization header set: ${_dio.options.headers.containsKey("Authorization")}');
      if (_dio.options.headers.containsKey("Authorization")) {
        final authHeader = _dio.options.headers["Authorization"] as String;
        final tokenPreview = authHeader.length > 30 ? '${authHeader.substring(0, 30)}...' : authHeader;
        print('🔑 DioClient: Auth header preview: $tokenPreview');
      }
    }
    _isInitialized = true;
  }
  
  /// Load stored tokens and apply them to the client headers
  Future<void> _loadStoredTokens() async {
    try {
      final accessToken = await _tokenManager.getAccessToken();
      if (accessToken != null && accessToken.isNotEmpty) {
        _dio.options.headers['Authorization'] = 'Bearer $accessToken';
        _tokensLoaded = true;
        
        if (kDebugMode) {
          print('🔑 DioClient: Loaded stored access token');
          print('📋 DioClient: Authorization header set for authenticated requests');
        }
      } else {
        // Remove any existing authorization header
        _dio.options.headers.remove('Authorization');
        _tokensLoaded = false;
        
        if (kDebugMode) {
          print('🔑 DioClient: No stored token found');
          print('📋 DioClient: Authorization header removed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ DioClient: Error loading stored tokens: $e');
      }
      _tokensLoaded = false;
    }
  }
  
  /// Ensure tokens are loaded and synchronized with the client
  Future<void> ensureTokensLoaded() async {
    if (!_tokensLoaded) {
      await _loadStoredTokens();
    }
  }
  
  /// Update the auth token directly - useful for immediate token updates
  /// This is used when we need to ensure the token is available immediately
  /// after operations like login or email verification
  void updateAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
    _tokensLoaded = true;
    
    if (kDebugMode) {
      print('🔑 DioClient: Updated auth token directly');
    }
  }
  
  /// Synchronize tokens from storage to the client headers
  /// This method ensures the client has the latest tokens from storage
  Future<void> syncTokensFromStorage() async {
    await _loadStoredTokens();
  }
  
  /// Clear tokens from the client headers
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
    _tokensLoaded = false;
    
    if (kDebugMode) {
      print('🔑 DioClient: Cleared auth token');
    }
  }
  
  /// Check if client has tokens loaded
  bool get hasTokensLoaded => _tokensLoaded;
  
  /// Check if client is fully initialized with interceptors
  bool get isInitialized => _isInitialized;
  
  /// Debug method to check if DioClient is properly initialized
  Map<String, dynamic> getDebugInfo() {
    return {
      'isInitialized': _isInitialized,
      'tokensLoaded': _tokensLoaded,
      'interceptorsCount': _dio.interceptors.length,
      'interceptorTypes': _dio.interceptors.map((e) => e.runtimeType.toString()).toList(),
      'hasAuthHeader': _dio.options.headers.containsKey('Authorization'),
      'baseUrl': _dio.options.baseUrl,
    };
  }
  
  /// Helper method to create silent options by merging with existing options
  Options _createSilentOptions(Options? options) {
    return Options(
      extra: {'showAutoToast': false, ...?options?.extra},
      headers: options?.headers,
      method: options?.method,
      responseType: options?.responseType,
      contentType: options?.contentType,
      validateStatus: options?.validateStatus,
      receiveDataWhenStatusError: options?.receiveDataWhenStatusError,
      followRedirects: options?.followRedirects,
      maxRedirects: options?.maxRedirects,
      requestEncoder: options?.requestEncoder,
      responseDecoder: options?.responseDecoder,
      listFormat: options?.listFormat,
    );
  }
  
  /// GET request with error handling and retry
  /// Set [showSuccessToast] to true to show a toast on successful responses
  Future<dynamic> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    // Ensure DioClient is fully initialized before making request
    await _ensureInitialized();
    
    try {
      if (retry) {
        // With retry logic for network failures
        return await _retryHandler.retryRequest(() async {
          final response = await _dio.get(
            path,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onReceiveProgress: onReceiveProgress,
          );
          
          return ResponseHandler.processResponse(response);
        });
      } else {
        // Without retry
        final response = await _dio.get(
          path,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onReceiveProgress: onReceiveProgress,
        );
        
        return ResponseHandler.processResponse(response, showSuccessToast: showSuccessToast);
      }
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    }
  }
  
  /// Silent GET request - no automatic toasts, for use with manual toast handling
  Future<dynamic> getSilent(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    return get(
      path,
      queryParameters: queryParameters,
      options: _createSilentOptions(options),
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
      retry: retry,
    );
  }
  
  /// POST request with error handling and retry
  /// Set [showSuccessToast] to true to show a toast on successful responses
  Future<dynamic> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    // Ensure DioClient is fully initialized before making request
    await _ensureInitialized();
    
    try {
      if (retry) {
        // With retry logic for network failures
        return await _retryHandler.retryRequest(() async {
          final response = await _dio.post(
            path,
            data: data,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress,
          );
          
          return ResponseHandler.processResponse(response);
        });
      } else {
        // Without retry
        final response = await _dio.post(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        );
        
        return ResponseHandler.processResponse(response, showSuccessToast: showSuccessToast);
      }
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    }
  }

  /// Silent POST request - no automatic toasts, for use with manual toast handling
  Future<dynamic> postSilent(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    return post(
      path,
      data: data,
      queryParameters: queryParameters,
      options: _createSilentOptions(options),
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      retry: retry,
    );
  }

  /// PUT request with error handling and retry
  /// Set [showSuccessToast] to true to show a toast on successful responses
  Future<dynamic> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    // Ensure DioClient is fully initialized before making request
    await _ensureInitialized();
    
    try {
      if (retry) {
        // With retry logic for network failures
        return await _retryHandler.retryRequest(() async {
          final response = await _dio.put(
            path,
            data: data,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress,
          );
          
          return ResponseHandler.processResponse(response);
        });
      } else {
        // Without retry
        final response = await _dio.put(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        );
        
        return ResponseHandler.processResponse(response, showSuccessToast: showSuccessToast);
      }
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    }
  }

  /// Silent PUT request - no automatic toasts, for use with manual toast handling
  Future<dynamic> putSilent(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    bool retry = true,
  }) async {
    return put(
      path,
      data: data,
      queryParameters: queryParameters,
      options: _createSilentOptions(options),
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      retry: retry,
    );
  }

  /// DELETE request with error handling and retry
  /// Set [showSuccessToast] to true to show a toast on successful responses
  Future<dynamic> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    try {
      if (retry) {
        // With retry logic for network failures
        return await _retryHandler.retryRequest(() async {
          final response = await _dio.delete(
            path,
            data: data,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
          );
          
          return ResponseHandler.processResponse(response);
        });
      } else {
        // Without retry
        final response = await _dio.delete(
          path,
          data: data,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
        );
        
        return ResponseHandler.processResponse(response, showSuccessToast: showSuccessToast);
      }
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    }
  }

  /// Silent DELETE request - no automatic toasts, for use with manual toast handling
  Future<dynamic> deleteSilent(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool retry = true,
  }) async {
    return delete(
      path,
      data: data,
      queryParameters: queryParameters,
      options: _createSilentOptions(options),
      cancelToken: cancelToken,
      retry: retry,
    );
  }
  
  /// Upload file to the server
  /// Set [showSuccessToast] to true to show a toast on successful responses
  Future<dynamic> uploadFile(
    String path, {
    required File file,
    required String fieldName,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    bool retry = true,
    bool showSuccessToast = false,
  }) async {
    // Wait for connectivity if offline
    await _connectivityHandler.waitForConnectivityIfOffline();
    
    try {
      if (retry) {
        // With retry logic for network failures (not for user cancellation)
        return await _retryHandler.retryRequest(() async {
          // Prepare form data
          final formData = FormData();
          
          // Add file with proper MIME type
          final fileName = file.path.split('/').last;
          final fileExtension = fileName.split('.').last.toLowerCase();
          
          // Set content type based on file extension
          String contentType;
          switch (fileExtension) {
            case 'jpg':
            case 'jpeg':
              contentType = 'image/jpeg';
              break;
            case 'png':
              contentType = 'image/png';
              break;
            case 'pdf':
              contentType = 'application/pdf';
              break;
            default:
              // Default to png if unknown
              contentType = 'image/png';
          }
          
          if (kDebugMode) {
            print('📤 Uploading file: $fileName with MIME type: $contentType');
          }
          
          formData.files.add(MapEntry(
            fieldName,
            await MultipartFile.fromFile(
              file.path,
              filename: fileName,
              contentType: MediaType.parse(contentType),
            ),
          ));
          
          // Add other fields
          if (data != null) {
            data.forEach((key, value) {
              formData.fields.add(MapEntry(key, value.toString()));
            });
          }
          
          // Send request
          final response = await _dio.post(
            path,
            data: formData,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
          );
          
          return ResponseHandler.processResponse(response);
        });
      } else {
        // Without retry - prepare form data
        final formData = FormData();
        
        // Add file with proper MIME type
        final fileName = file.path.split('/').last;
        final fileExtension = fileName.split('.').last.toLowerCase();
        
        // Set content type based on file extension
        String contentType;
        switch (fileExtension) {
          case 'jpg':
          case 'jpeg':
            contentType = 'image/jpeg';
            break;
          case 'png':
            contentType = 'image/png';
            break;
          case 'pdf':
            contentType = 'application/pdf';
            break;
          default:
            // Default to png if unknown
            contentType = 'image/png';
        }
        
        if (kDebugMode) {
          print('📤 Uploading file: $fileName with MIME type: $contentType');
        }
        
        formData.files.add(MapEntry(
          fieldName,
          await MultipartFile.fromFile(
            file.path,
            filename: fileName,
            contentType: MediaType.parse(contentType),
          ),
        ));
        
        // Add other fields
        if (data != null) {
          data.forEach((key, value) {
            formData.fields.add(MapEntry(key, value.toString()));
          });
        }
        
        // Send request
        final response = await _dio.post(
          path,
          data: formData,
          queryParameters: queryParameters,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
        );
        
        return ResponseHandler.processResponse(response, showSuccessToast: showSuccessToast);
      }
    } on DioException catch (e) {
      throw ErrorHandler.handleDioError(e);
    }
  }
  
  /// Access to the token manager for direct token operations
  TokenManager get tokenManager => _tokenManager;

  /// Ensure DioClient is fully initialized before making requests
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('🔧 DioClient: Request attempted before initialization, calling init()...');
      }
      await init();
    }
  }
}
