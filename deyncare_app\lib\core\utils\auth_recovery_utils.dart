import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:deyncare_app/data/network/token/token_manager.dart';
import 'package:deyncare_app/data/utils/auth_utils.dart';
import 'package:deyncare_app/data/network/clients/dio_client.dart';

/// Utility class for authentication recovery scenarios
class AuthRecoveryUtils {
  static final Logger _logger = Logger();

  /// Check if user has lost tokens but still has user data
  /// This indicates a potential premature token clearing issue
  static Future<bool> hasTokenLossIssue({
    required TokenManager tokenManager,
    required AuthUtils authUtils,
  }) async {
    try {
      final hasTokens = await _hasValidTokens(tokenManager);
      final hasUserData = await _hasUserData(authUtils);
      
      // If we have user data but no tokens, this suggests token loss
      final hasIssue = !hasTokens && hasUserData;
      
      if (hasIssue) {
        _logger.w('AuthRecoveryUtils: Token loss detected - user data exists but no tokens');
      }
      
      return hasIssue;
    } catch (e) {
      _logger.e('AuthRecoveryUtils: Error checking token loss: $e');
      return false;
    }
  }

  /// Get detailed authentication state for debugging
  static Future<Map<String, dynamic>> getAuthState({
    required TokenManager tokenManager,
    required AuthUtils authUtils,
    required DioClient dioClient,
  }) async {
    try {
      final tokenInfo = await tokenManager.getTokenInfo();
      final userData = await authUtils.getUserData();
      
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'tokenInfo': tokenInfo,
        'hasUserData': userData != null,
        'userEmail': userData?.email,
        'userStatus': userData?.status,
        'userRole': userData?.role,
        'dioClientHasTokens': dioClient.hasTokensLoaded,
        'tokenLossDetected': await hasTokenLossIssue(
          tokenManager: tokenManager,
          authUtils: authUtils,
        ),
      };
    } catch (e) {
      _logger.e('AuthRecoveryUtils: Error getting auth state: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Log authentication state for debugging
  static Future<void> logAuthState({
    required TokenManager tokenManager,
    required AuthUtils authUtils,
    required DioClient dioClient,
    required String context,
  }) async {
    if (kDebugMode) {
      final authState = await getAuthState(
        tokenManager: tokenManager,
        authUtils: authUtils,
        dioClient: dioClient,
      );
      
      print('🔐 AUTH STATE [$context]:');
      print('  Timestamp: ${authState['timestamp']}');
      print('  Has User Data: ${authState['hasUserData']}');
      print('  User Email: ${authState['userEmail']}');
      print('  User Status: ${authState['userStatus']}');
      print('  User Role: ${authState['userRole']}');
      print('  DioClient Has Tokens: ${authState['dioClientHasTokens']}');
      print('  Token Loss Detected: ${authState['tokenLossDetected']}');
      
      if (authState.containsKey('tokenInfo')) {
        final tokenInfo = authState['tokenInfo'] as Map<String, dynamic>;
        print('  Token Info:');
        print('    - Has Access Token: ${tokenInfo['hasAccessToken']}');
        print('    - Has Refresh Token: ${tokenInfo['hasRefreshToken']}');
        print('    - Is Expired: ${tokenInfo['isExpired']}');
        print('    - Should Refresh: ${tokenInfo['shouldRefresh']}');
        print('    - Time Until Expiry: ${tokenInfo['timeUntilExpiry']} minutes');
      }
    }
  }

  /// Attempt to recover authentication state
  static Future<bool> attemptRecovery({
    required TokenManager tokenManager,
    required AuthUtils authUtils,
    required DioClient dioClient,
  }) async {
    try {
      _logger.d('AuthRecoveryUtils: Attempting authentication recovery');
      
      // Check if we have user data but no tokens
      final hasTokenLoss = await hasTokenLossIssue(
        tokenManager: tokenManager,
        authUtils: authUtils,
      );
      
      if (hasTokenLoss) {
        _logger.w('AuthRecoveryUtils: Token loss detected, user needs to re-authenticate');
        
        // Clear any remaining inconsistent state
        await tokenManager.clearTokens();
        await authUtils.clearUserData();
        dioClient.clearAuthToken();
        
        _logger.d('AuthRecoveryUtils: Cleared inconsistent auth state');
        return false; // Indicates user needs to login again
      }
      
      // If we have both tokens and user data, try to sync DioClient
      final hasTokens = await _hasValidTokens(tokenManager);
      final hasUserData = await _hasUserData(authUtils);
      
      if (hasTokens && hasUserData) {
        final accessToken = await tokenManager.getAccessToken();
        if (accessToken != null) {
          dioClient.updateAuthToken(accessToken);
          _logger.d('AuthRecoveryUtils: Synced token with DioClient');
          return true;
        }
      }
      
      return false;
    } catch (e) {
      _logger.e('AuthRecoveryUtils: Error during recovery attempt: $e');
      return false;
    }
  }

  /// Check if user has valid tokens
  static Future<bool> _hasValidTokens(TokenManager tokenManager) async {
    try {
      final accessToken = await tokenManager.getAccessToken();
      final refreshToken = await tokenManager.getRefreshToken();
      return accessToken != null && refreshToken != null;
    } catch (e) {
      return false;
    }
  }

  /// Check if user has stored user data
  static Future<bool> _hasUserData(AuthUtils authUtils) async {
    try {
      final userData = await authUtils.getUserData();
      return userData != null;
    } catch (e) {
      return false;
    }
  }
}
